"use client";

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiCopy, FiExternalLink, FiEdit2, FiTrash2, FiDownload, FiUpload } from 'react-icons/fi';
import { FaEthereum } from 'react-icons/fa';
import { SiBinance } from 'react-icons/si';
import { useClickAway } from 'react-use';
import { createPortal } from 'react-dom';

// Anubis theme colors
const ANUBIS_THEME = {
  primary: '#d4af37', // Gold
  secondary: '#1a1a1a', // Dark background
  accent: '#8b5a2b', // Brown accent
  text: '#f0f0f0', // Light text
  cardBg: 'rgba(26, 26, 26, 0.8)', // Semi-transparent dark
  border: 'rgba(212, 175, 55, 0.2)', // Gold border
  hover: 'rgba(212, 175, 55, 0.1)', // Gold hover
};

const truncateAddress = (address: string, start = 6, end = 4) => {
  if (!address) return '';
  return `${address.slice(0, start)}...${address.slice(-end)}`;
};

const getChainIcon = (chain: string) => {
  switch (chain.toLowerCase()) {
    case 'eth':
      return <FaEthereum className="text-indigo-400" />;
    case 'bsc':
      return <SiBinance className="text-yellow-500" />;
    default:
      return <div className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center">
        <span className="text-xs font-bold">{chain.charAt(0)}</span>
      </div>;
  }
};

interface WalletCardProps {
  name: string;
  address: string;
  balance: string;
  chain: string;
  onDeposit: () => void;
  onWithdraw: () => void;
  onExport: () => void;
  onEdit: () => void;
  onArchive: () => void;
}

const WalletCard = ({
  name,
  address,
  balance,
  chain,
  onDeposit,
  onWithdraw,
  onExport,
  onEdit,
  onArchive,
}: WalletCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number } | null>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Close dropdown when clicking outside
  useClickAway(dropdownRef, () => {
    if (isDropdownOpen) setIsDropdownOpen(false);
  });

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    if (isDropdownOpen) setIsDropdownOpen(false);
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDropdownOpen(!isDropdownOpen);
    if (!isDropdownOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY + 4,
        left: rect.right - 224 + window.scrollX, // 224px = dropdown width
      });
    }
  };

  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(address);
    // Optional: Add toast notification
  };

  const handleAction = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation();
    action();
    setIsDropdownOpen(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.2 }}
      className={`relative p-5 rounded-xl border shadow-sm bg-white border-gray-200 transition-all duration-300 cursor-pointer overflow-hidden ${isExpanded ? 'ring-2 ring-black' : ''}`}
      onClick={toggleExpand}
    >
      {/* Animated border highlight on hover */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-900/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>

      <div className="relative z-10">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="mt-1">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-700 to-blue-900 flex items-center justify-center shadow-lg">
                {getChainIcon(chain)}
              </div>
            </div>
            <div>
              <h4 className="font-bold text-black flex items-center">
                {name}
                <span className="ml-2 px-2 py-0.5 text-xs bg-gray-200 text-black rounded-full">
                  {chain}
                </span>
              </h4>
              <div className="flex items-center mt-1">
                <span className="text-sm text-gray-400 font-mono">
                  {truncateAddress(address)}
                </span>
                <button
                  onClick={handleCopy}
                  className="ml-2 p-1 rounded-full text-black hover:bg-gray-100 transition-colors"
                  aria-label="Copy address"
                >
                  <FiCopy size={14} />
                </button>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="text-right">
              <p className="text-xs font-medium text-gray-700">BALANCE</p>
              <p className="text-lg font-bold text-black">
                ${balance}
              </p>
            </div>

            <div className="relative z-20">
              <button
                ref={buttonRef}
                onClick={toggleDropdown}
                className="p-1.5 rounded-lg hover:bg-gray-100 text-black hover:text-black transition-colors"
                aria-label="Wallet actions"
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="1" />
                  <circle cx="12" cy="5" r="1" />
                  <circle cx="12" cy="19" r="1" />
                </svg>
              </button>
              {typeof window !== 'undefined' && isDropdownOpen && dropdownPosition &&
                createPortal(
                  <motion.div
                    initial={{ opacity: 0, y: 10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 10, scale: 0.95 }}
                    transition={{ duration: 0.15, ease: "easeOut" }}
                    className="fixed w-56 origin-top-right rounded-xl bg-white border border-gray-200 overflow-hidden z-[9999]"
                    style={{
                      top: dropdownPosition.top,
                      left: dropdownPosition.left,
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    }}
                  >
                    <div className="py-1">
                      <button
                        onClick={(e) => handleAction(e, onDeposit)}
                        className="w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors"
                      >
                        <FiDownload className="mr-3 text-gray-700" size={16} />
                        <span>Deposit</span>
                      </button>
                      <button
                        onClick={(e) => handleAction(e, onWithdraw)}
                        className="w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors"
                      >
                        <FiUpload className="mr-3 text-gray-700" size={16} />
                        <span>Withdraw</span>
                      </button>
                      <button
                        onClick={(e) => handleAction(e, onExport)}
                        className="w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors"
                      >
                        <FiDownload className="mr-3 text-gray-700" size={16} />
                        <span>Export Private Key</span>
                      </button>
                      <div className="border-t border-gray-200 my-1"></div>
                      <button
                        onClick={(e) => handleAction(e, onEdit)}
                        className="w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors"
                      >
                        <FiEdit2 className="mr-3 text-gray-700" size={16} />
                        <span>Edit Wallet</span>
                      </button>
                      <button
                        onClick={(e) => handleAction(e, onArchive)}
                        className="w-full flex items-center px-4 py-2.5 text-sm text-red-600 hover:bg-red-100 transition-colors"
                      >
                        <FiTrash2 className="mr-3" size={16} />
                        <span>Archive Wallet</span>
                      </button>
                    </div>
                  </motion.div>,
                  document.body
                )
              }
            </div>

            <button
              className="p-1.5 rounded-lg hover:bg-gray-100 text-black hover:text-black transition-colors"
              onClick={toggleExpand}
              aria-label={isExpanded ? 'Collapse' : 'Expand'}
            >
              <svg
                className={`w-5 h-5 transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0, marginTop: 0 }}
              animate={{ height: 'auto', opacity: 1, marginTop: '1rem' }}
              exit={{ height: 0, opacity: 0, marginTop: 0 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <div className="pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="text-sm font-semibold text-gray-700">ASSETS</h5>
                  <span className="text-xs text-gray-400">Value</span>
                </div>

                <div className="space-y-3">
                  {/* Example asset - in a real app, this would be mapped from wallet assets */}
                  <div className="flex items-center justify-between p-3 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                        <FaEthereum className="text-black" size={14} />
                      </div>
                      <div>
                        <div className="font-medium text-black">Ethereum</div>
                        <div className="text-xs text-gray-700">ETH</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-black">$3,450.25</div>
                      <div className="text-xs text-gray-700">0.5 ETH</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center">
                        <SiBinance className="text-black" size={14} />
                      </div>
                      <div>
                        <div className="font-medium text-black">USD Coin</div>
                        <div className="text-xs text-gray-700">USDC</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-black">$1,250.75</div>
                      <div className="text-xs text-green-600">1,250.75 USDC</div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-3 border-t border-gray-200">
                  <button className="w-full py-2 px-4 bg-black hover:bg-gray-900 text-white rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-2">
                    <span>View All Assets</span>
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default WalletCard;
