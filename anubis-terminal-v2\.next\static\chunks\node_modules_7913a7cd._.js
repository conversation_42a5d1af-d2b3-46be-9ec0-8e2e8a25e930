(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/viem/_esm/utils/ccip.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_viem__esm_125ff6eb._.js",
  "static/chunks/node_modules_viem__esm_utils_ccip_c871f3cc.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/viem/_esm/utils/ccip.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript)");
    });
});
}}),
}]);