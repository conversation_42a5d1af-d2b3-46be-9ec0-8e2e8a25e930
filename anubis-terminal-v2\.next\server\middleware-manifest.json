{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RbFBlz2F4Sj1xnBfLFiovwITVbxvWgVA/mPAaZqhI1M=", "__NEXT_PREVIEW_MODE_ID": "f31a21c100398ce86f07a3a90a0eebb7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "285fbc355c9280986d34f6b9c764890a7acf0074b8cd48fbac3d81c481aa7e07", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "de5d9c29224f8daccf57b5ae8571a3b2198024f1406b4af0e60e60a062047950"}}}, "sortedMiddleware": ["/"], "functions": {}}