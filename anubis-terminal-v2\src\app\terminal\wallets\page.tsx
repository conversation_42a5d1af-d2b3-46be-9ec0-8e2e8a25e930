"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useUserData } from "@/hooks/useUserData";
import { useRouter } from "next/navigation";
import { useEffect, useState, useCallback, useMemo } from "react";
import { FiPlus, FiFrown as Frown, FiUpload as Upload, FiSearch, FiCopy, FiExternalLink, FiTrash2, FiEdit2, FiCheck, FiX, FiDownload } from "react-icons/fi";
import { motion, AnimatePresence } from "framer-motion";
import WalletCard from "@/components/Terminal/Wallets/WalletCard";
import WalletSelector from "@/components/Terminal/Wallets/Dropdown";
import { Wallet, createWallet, getWallets, importWallet, setPrimaryWallet, getWalletPrivateKey, getWalletBalance } from "@/services/bot";
import { getBalanceForEachChain } from "@/services/viem";
import { chains } from "@/utils/data";
import { useWeb3 } from "@/contexts/Web3Context";
import { toast } from 'react-toastify';

// Extended Wallet type with UI-specific properties
interface UIExtendedWallet extends Wallet {
  id: string; // Use address as ID
  name: string;
  chain: string;
  balance: number;
  isPrimary: boolean;
  isTrading: boolean;
  assets?: Array<{
    symbol: string;
    balance: string;
    usdValue: string;
  }>;
  nativeBalances?: Array<{
    name: string;
    balance: bigint;
  }>;
}

// Helper function to format currency
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.3 } },
};

export default function Wallets() {
  const { token } = useAuth();
  const { data: userData } = useUserData(token);
  const { selectedChain } = useWeb3();
  const router = useRouter();
  const [redirecting, setRedirecting] = useState(false);
  // State for wallets and UI
  const [wallets, setWallets] = useState<UIExtendedWallet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [importStep, setImportStep] = useState<1 | 2>(1);
  const [importPrivateKey, setImportPrivateKey] = useState('');
  const [importWalletName, setImportWalletName] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);
  const [walletName, setWalletName] = useState('');
  const [privateKey, setPrivateKey] = useState('');
  const [expandedWallet, setExpandedWallet] = useState<string | null>(null);
  const [exportedKey, setExportedKey] = useState<string | null>(null);
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportWalletId, setExportWalletId] = useState<string | null>(null);
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [depositWallet, setDepositWallet] = useState<UIExtendedWallet | null>(null);

  // Fetch wallets and enrich with native balances
  const fetchWallets = useCallback(async () => {
    if (!token) return;
    setLoading(true);
    setError(null);
    try {
      const response = await getWallets(token);
      if (Array.isArray(response)) {
        // Enrich wallets with native balances from viem
        const walletsWithUIProps: UIExtendedWallet[] = await Promise.all(
          response.map(async (wallet: any) => {
            let nativeBalances: Array<{ name: string; balance: bigint }> = [];
            try {
              nativeBalances = await getBalanceForEachChain(wallet.address);
            } catch (e) {
              nativeBalances = [];
            }
            return {
              ...wallet,
              id: wallet.address,
              name: `Wallet ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`,
              chain: 'Multi',
              balance: 0, // Will be updated by getWalletBalance if needed
              isPrimary: wallet.isPrimary || false,
              isTrading: true,
              assets: [],
              nativeBalances,
            };
          })
        );
        setWallets(walletsWithUIProps);
      } else if (response && typeof response === 'object' && 'error' in response) {
        const errorResponse = response as { error: string };
        throw new Error(errorResponse.error);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load wallets';
      setError(`Failed to load wallets: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, [token]);

  // Initial fetch
  useEffect(() => {
    if (token) {
      fetchWallets();
    }
  }, [token, fetchWallets]);

  // Handle redirect if no token
  useEffect(() => {
    if (!token && !redirecting) {
      setRedirecting(true);
      router.replace('/terminal/error');
    }
  }, [token, redirecting, router]);

  // Filter wallets based on search query
  const filteredWallets = useMemo(() => {
    if (!searchQuery.trim()) return wallets;

    const query = searchQuery.toLowerCase().trim();
    return wallets.filter(wallet =>
      wallet.address.toLowerCase().includes(query) ||
      wallet.name.toLowerCase().includes(query)
    );
  }, [wallets, searchQuery]);

  // Handle wallet creation
  const handleCreateWallet = async () => {
    if (!token) return;

    setIsCreating(true);
    try {
      const newWallet = await createWallet(token);
      if (newWallet && 'address' in newWallet) {
        await fetchWallets(); // Refresh the list
        setShowCreateModal(false);
        // TODO: Show success toast
      } else {
        throw new Error('Invalid wallet creation response');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Failed to create wallet:', errorMessage);
      setError(`Failed to create wallet: ${errorMessage}`);
    } finally {
      setIsCreating(false);
    }
  };

  // Handle wallet import
  const handleImportWallet = async () => {
    if (!token || !privateKey) return;

    setIsSubmitting(true);
    try {
      const result = await importWallet(token, { privateKey });
      if (result && 'address' in result) {
        await fetchWallets(); // Refresh the list
        setShowImportModal(false);
        setPrivateKey('');
        setWalletName('');
        // TODO: Show success toast
      } else {
        throw new Error('Invalid import response');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Invalid private key';
      console.error('Failed to import wallet:', errorMessage);
      setError(`Failed to import wallet: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle set primary wallet
  const handleSetPrimary = async (walletAddress: `0x${string}`) => {
    if (!token) return;

    try {
      await setPrimaryWallet(token, walletAddress);
      await fetchWallets(); // Refresh the list
      // TODO: Show success toast
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Failed to set primary wallet:', errorMessage);
      setError(`Failed to set primary wallet: ${errorMessage}`);
    }
  };

  // Handle copy to clipboard
  const handleCopyAddress = (address: string) => {
    navigator.clipboard.writeText(address).catch(err => {
      console.error('Failed to copy address:', err);
      setError('Failed to copy address to clipboard');
    });
    setCopiedAddress(address);
    toast.success('Address copied to clipboard');
    setTimeout(() => setCopiedAddress(null), 2000);
  };

  // Handle view on explorer
  const handleViewOnExplorer = (address: string) => {
    try {
      const explorerUrl = selectedChain?.scanUrl || 'https://etherscan.io';
      const url = `${explorerUrl}/address/${address}`;

      window.open(url, '_blank', 'noopener,noreferrer');
    } catch (err) {
      console.error('Failed to open explorer:', err);
      setError('Failed to open blockchain explorer');
    }
  };

  // Handle wallet archive
  const handleArchive = async (walletId: string) => {
    if (!token) return;

    try {
      // TODO: Implement archive wallet API call when available
      console.log('Archive wallet:', walletId);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Optimistic update
      setWallets(prevWallets =>
        prevWallets.filter(wallet => wallet.id !== walletId)
      );

      // TODO: Show success toast when API is implemented
      console.log('Wallet archived successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to archive wallet';
      console.error('Failed to archive wallet:', errorMessage);
      setError(`Failed to archive wallet: ${errorMessage}`);
      // Re-fetch to revert optimistic update on error
      fetchWallets();
    }
  };

  // Handle edit wallet name
  const handleEditName = (walletId: string, newName: string) => {
    // Optimistic update
    setWallets(prevWallets =>
      prevWallets.map(wallet =>
        wallet.id === walletId ? { ...wallet, name: newName } : wallet
      )
    );

    // TODO: Implement API call to update wallet name when available
    console.log(`Updating wallet ${walletId} name to:`, newName);
  };

  // Handle wallet export (placeholder for future implementation)
  const handleExportWallet = async (walletId: string) => {
    if (!token) return;

    try {
      // TODO: Implement secure export when API is available
      console.log('Export wallet requested for:', walletId);
      alert('Wallet export functionality will be available in a future update.');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export wallet';
      console.error('Failed to export wallet:', errorMessage);
      setError(`Failed to export wallet: ${errorMessage}`);
    }
  };

  // Handler for deposit action
  const handleDeposit = (walletId: string) => {
    const wallet = wallets.find(w => w.id === walletId);
    if (wallet) {
      setDepositWallet(wallet);
      setShowDepositModal(true);
    }
  };

  // Handler for withdraw action
  const handleWithdraw = (walletId: string) => {
    const wallet = wallets.find(w => w.id === walletId);
    if (wallet) {
      console.log(`Withdraw from ${wallet.name}`);
      // TODO: Implement withdraw flow
    }
  };

  // Handler for edit action
  const handleEdit = (walletId: string) => {
    const wallet = wallets.find(w => w.id === walletId);
    if (wallet) {
      const newName = prompt('Enter new wallet name:', wallet.name);
      if (newName && newName !== wallet.name) {
        handleEditName(walletId, newName);
      }
    }
  };

  // Calculate total balance (regular function, not a hook)
  const calculateTotalBalance = (wallets: UIExtendedWallet[]): number => {
    let total = 0;
    wallets.forEach(wallet => {
      if (wallet.nativeBalances) {
        wallet.nativeBalances.forEach(chainBal => {
          // For demo, just sum as ETH (should convert to USD with price API)
          total += Number(chainBal.balance) / 1e18;
        });
      }
    });
    return total;
  };

  // Memoize the total balance calculation
  const totalBalance = calculateTotalBalance(wallets);

  // Create handlers object
  const walletHandlers = {
    handleDeposit,
    handleWithdraw,
    handleExport: handleExportWallet,
    handleEdit,
    handleArchive,
    handleImport: handleImportWallet,
    handleCopyAddress,
    handleViewOnExplorer,
    handleSetPrimary
  };

  // Copy address for deposit modal
  const handleCopyDepositAddress = () => {
    if (depositWallet) {
      navigator.clipboard.writeText(depositWallet.address).then(() => {
        toast.success(`Address copied to clipboard (${depositWallet.chain})`);
      }).catch(() => {
        setError('Failed to copy address to clipboard');
      });
    }
  };

  // Multistep Import Modal Handlers
  const handleOpenImportModal = () => {
    setShowImportModal(true);
    setImportStep(1);
    setImportPrivateKey('');
    setImportWalletName('');
  };
  const handleCloseImportModal = () => {
    setShowImportModal(false);
    setImportStep(1);
    setImportPrivateKey('');
    setImportWalletName('');
  };
  const handleImportNext = () => {
    setImportStep(2);
  };
  const handleImportBack = () => {
    setImportStep(1);
  };
  const handleImportSubmit = async () => {
    if (!token || !importPrivateKey) return;
    setIsImporting(true);
    try {
      await importWallet(token, { privateKey: importPrivateKey, name: importWalletName });
      toast.success('Wallet imported successfully');
      handleCloseImportModal();
      fetchWallets();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to import wallet';
      setError(errorMessage);
    } finally {
      setIsImporting(false);
    }
  };

  // --- UI ---
  return (
    <div className="min-h-[calc(100vh-80px)] bg-black p-4 md:p-8">
      {/* Header */}
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-black">
              Wallets
            </h1>
            <p className="mt-2 text-white">
              Manage your cryptocurrency wallets and assets
            </p>
          </div>

          <div className="mt-4 md:mt-0 flex space-x-3">
            <button
              onClick={handleOpenImportModal}
              className="flex items-center px-4 py-2.5 bg-black text-white rounded-lg transition-colors border border-gray-200"
            >
              <Upload className="mr-2" size={16} />
              <span>Import</span>
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center px-4 py-2.5 bg-black text-white rounded-lg transition-colors border border-gray-200"
            >
              <FiPlus className="mr-2" size={16} />
              <span>New Wallet</span>
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-700">Total Balance ({selectedChain?.symbol})</p>
                <p className="text-2xl font-bold mt-1 text-black">
                  {formatCurrency(totalBalance)}
                </p>
              </div>
              <div className="p-3 rounded-full bg-gray-100 text-black">
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-700">Total Wallets</p>
                <p className="text-2xl font-bold mt-1 text-black">
                  {wallets.length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-gray-100 text-black">
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-700">Active Chains</p>
                <p className="text-2xl font-bold mt-1 text-black">
                  {new Set(wallets.flatMap(w => (w.nativeBalances || []).map(b => b.name))).size}
                </p>
              </div>
              <div className="p-3 rounded-full bg-gray-100 text-black">
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="mb-6">
          <div className="relative max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiSearch className="h-5 w-5 text-gray-700" />
            </div>
            <input
              type="text"
              placeholder="Search wallets by name or address..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2.5 bg-white border border-gray-200 rounded-lg text-black placeholder-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            />
          </div>
        </div>

        {/* Wallets List */}
        <div className="space-y-4 z-10">
          {loading ? (
            // Loading skeleton
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-24 bg-gray-100 rounded-xl animate-pulse"></div>
              ))}
            </div>
          ) : filteredWallets.length > 0 ? (
            // Wallets grid
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="show"
              className="grid grid-cols-1 gap-4 z-10"
            >
              <AnimatePresence>
                {filteredWallets.map((wallet) => (
                  <motion.div key={wallet.id} variants={itemVariants}>
                    <WalletCard
                      name={wallet.name}
                      address={wallet.address}
                      balance={wallet.nativeBalances?.map(b => `${b.name}: ${(Number(b.balance) / 1e18).toFixed(4)}`).join(' | ') || '0'}
                      chain={wallet.chain}
                      onDeposit={() => handleDeposit(wallet.id)}
                      onWithdraw={() => handleWithdraw(wallet.id)}
                      onExport={() => handleExportWallet(wallet.id)}
                      onEdit={() => handleEdit(wallet.id)}
                      onArchive={() => handleArchive(wallet.id)}
                    />
                    {/* Micro-interaction: Expand for details */}
                    {expandedWallet === wallet.id && (
                      <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 10 }} className="p-4 bg-gray-100 rounded-xl mt-2 border border-gray-200">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                          <div>
                            <div className="font-semibold text-gray-700 mb-2">Native Balances</div>
                            <ul className="text-sm text-black/80">
                              {wallet.nativeBalances?.map((b, i) => (
                                <li key={i}>{b.name}: {(Number(b.balance) / 1e18).toFixed(4)}</li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <button onClick={() => handleCopyAddress(wallet.address)} className="mr-2 px-3 py-1 bg-gray-100 hover:bg-gray-200 text-black rounded transition-colors">{copiedAddress === wallet.address ? <FiCheck /> : <FiCopy />} Copy Address</button>
                            <button onClick={() => handleViewOnExplorer(wallet.address)} className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-black rounded transition-colors"><FiExternalLink /> View on Explorer</button>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>
            </motion.div>
          ) : (
            // Empty state
            <div className="text-center py-16 bg-gray-100 rounded-xl border-2 border-dashed border-gray-200">
              <Frown className="mx-auto h-12 w-12 text-gray-700" />
              <h3 className="mt-2 text-lg font-medium text-gray-700">No wallets found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchQuery ? 'Try a different search term' : 'Get started by creating a new wallet'}
              </p>
              <div className="mt-6">
                <button
                  onClick={handleCreateWallet}
                  disabled={isCreating}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiPlus className="-ml-1 mr-2 h-5 w-5" />
                  New Wallet
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Multistep Import Wallet Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-black">Import Wallet</h3>
                <button
                  onClick={handleCloseImportModal}
                  className="text-gray-700 hover:text-black"
                >
                  <FiX size={24} />
                </button>
              </div>
              {importStep === 1 && (
                <div className="space-y-4">
                  <div>
                    <label htmlFor="import-wallet-name" className="block text-sm font-medium text-black mb-1">
                      Wallet Name (Optional)
                    </label>
                    <input
                      type="text"
                      id="import-wallet-name"
                      value={importWalletName}
                      onChange={(e) => setImportWalletName(e.target.value)}
                      placeholder="e.g. My Imported Wallet"
                      className="w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label htmlFor="import-private-key" className="block text-sm font-medium text-black mb-1">
                      Private Key
                    </label>
                    <textarea
                      id="import-private-key"
                      rows={3}
                      value={importPrivateKey}
                      onChange={(e) => setImportPrivateKey(e.target.value)}
                      placeholder="Enter your private key..."
                      className="w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-md text-black font-mono text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Your private key is encrypted and never leaves your device.
                    </p>
                  </div>
                  <div className="pt-2 flex justify-end gap-2">
                    <button
                      onClick={handleCloseImportModal}
                      className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-black rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleImportNext}
                      disabled={!importPrivateKey || isImporting}
                      className="px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
              {importStep === 2 && (
                <div className="space-y-4">
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                    <div className="text-sm text-gray-700 mb-2">Please confirm the details before importing:</div>
                    <div className="mb-1">
                      <span className="font-semibold">Wallet Name:</span> {importWalletName || <span className="italic text-gray-400">(None)</span>}
                    </div>
                    <div>
                      <span className="font-semibold">Private Key:</span>
                      <span className="ml-2 font-mono text-xs text-black bg-gray-100 px-2 py-1 rounded select-all">{importPrivateKey.slice(0, 6)}...{importPrivateKey.slice(-4)}</span>
                    </div>
                  </div>
                  <div className="pt-2 flex justify-between gap-2">
                    <button
                      onClick={handleImportBack}
                      className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-black rounded-md"
                    >
                      Back
                    </button>
                    <button
                      onClick={handleImportSubmit}
                      disabled={isImporting}
                      className="px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isImporting ? 'Importing...' : 'Import Wallet'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Create Wallet Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-black">Create New Wallet</h3>
                <button onClick={() => setShowCreateModal(false)} className="text-gray-700 hover:text-white">
                  <FiX size={24} />
                </button>
              </div>
              <div className="space-y-4">
                <p className="text-gray-700">A new wallet will be generated and securely added to your account.</p>
                <div className="pt-2">
                  <button onClick={handleCreateWallet} disabled={isCreating} className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed">
                    {isCreating ? 'Creating...' : 'Create Wallet'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Export Wallet Modal */}
      {showExportModal && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-black">Export Private Key</h3>
                <button onClick={() => setShowExportModal(false)} className="text-gray-700 hover:text-white">
                  <FiX size={24} />
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-black mb-1">Private Key</label>
                  <textarea readOnly value={exportedKey || ''} className="w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-md text-black font-mono text-sm focus:outline-none" />
                  <p className="mt-1 text-xs text-gray-500">Keep your private key safe. Never share it with anyone.</p>
                </div>
                <div className="pt-2 flex justify-end">
                  <button onClick={() => setShowExportModal(false)} className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-black rounded-md">Close</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Deposit Modal */}
      {showDepositModal && depositWallet && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-black">Deposit</h3>
                <button
                  onClick={() => setShowDepositModal(false)}
                  className="text-gray-700 hover:text-black"
                >
                  <FiX size={24} />
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <img src={selectedChain?.logo} alt="" className="bg-black rounded-md" />
                  <div className="text-sm font-medium text-black mb-1">Wallet Address</div>
                  <div className="flex items-center bg-gray-100 border border-gray-200 rounded-md px-3 py-2">
                    <span className="font-mono text-black text-xs break-all select-all">{depositWallet.address}</span>
                    <button
                      onClick={handleCopyDepositAddress}
                      className="ml-2 p-1 rounded-full text-black hover:bg-gray-200 transition-colors"
                      aria-label="Copy address"
                    >
                      <FiCopy size={16} />
                    </button>
                  </div>
                </div>
                <div className="pt-2 flex justify-end">
                  <button
                    onClick={() => setShowDepositModal(false)}
                    className="px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-md"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Toast */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-700 text-white px-4 py-2 rounded shadow-lg z-50 animate-pulse">
          {error}
          <button className="ml-2" onClick={() => setError(null)}><FiX /></button>
        </div>
      )}
    </div>
  );
}
