{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Wallets/WalletCard/index.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FiCopy, FiExternalLink, FiEdit2, FiTrash2, FiDownload, FiUpload } from 'react-icons/fi';\nimport { FaEthereum } from 'react-icons/fa';\nimport { SiBinance } from 'react-icons/si';\nimport { useClickAway } from 'react-use';\nimport { createPortal } from 'react-dom';\n\n// Anubis theme colors\nconst ANUBIS_THEME = {\n  primary: '#d4af37', // Gold\n  secondary: '#1a1a1a', // Dark background\n  accent: '#8b5a2b', // Brown accent\n  text: '#f0f0f0', // Light text\n  cardBg: 'rgba(26, 26, 26, 0.8)', // Semi-transparent dark\n  border: 'rgba(212, 175, 55, 0.2)', // Gold border\n  hover: 'rgba(212, 175, 55, 0.1)', // Gold hover\n};\n\nconst truncateAddress = (address: string, start = 6, end = 4) => {\n  if (!address) return '';\n  return `${address.slice(0, start)}...${address.slice(-end)}`;\n};\n\nconst getChainIcon = (chain: string) => {\n  switch (chain.toLowerCase()) {\n    case 'eth':\n      return <FaEthereum className=\"text-indigo-400\" />;\n    case 'bsc':\n      return <SiBinance className=\"text-yellow-500\" />;\n    default:\n      return <div className=\"w-6 h-6 rounded-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center\">\n        <span className=\"text-xs font-bold\">{chain.charAt(0)}</span>\n      </div>;\n  }\n};\n\ninterface WalletCardProps {\n  name: string;\n  address: string;\n  balance: string;\n  chain: string;\n  onDeposit: () => void;\n  onWithdraw: () => void;\n  onExport: () => void;\n  onEdit: () => void;\n  onArchive: () => void;\n}\n\nconst WalletCard = ({\n  name,\n  address,\n  balance,\n  chain,\n  onDeposit,\n  onWithdraw,\n  onExport,\n  onEdit,\n  onArchive,\n}: WalletCardProps) => {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number } | null>(null);\n  const buttonRef = useRef<HTMLButtonElement>(null);\n\n  // Close dropdown when clicking outside\n  useClickAway(dropdownRef, () => {\n    if (isDropdownOpen) setIsDropdownOpen(false);\n  });\n\n  const toggleExpand = () => {\n    setIsExpanded(!isExpanded);\n    if (isDropdownOpen) setIsDropdownOpen(false);\n  };\n\n  const toggleDropdown = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setIsDropdownOpen(!isDropdownOpen);\n    if (!isDropdownOpen && buttonRef.current) {\n      const rect = buttonRef.current.getBoundingClientRect();\n      setDropdownPosition({\n        top: rect.bottom + window.scrollY + 4,\n        left: rect.right - 224 + window.scrollX, // 224px = dropdown width\n      });\n    }\n  };\n\n  const handleCopy = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    navigator.clipboard.writeText(address);\n    // Optional: Add toast notification\n  };\n\n  const handleAction = (e: React.MouseEvent, action: () => void) => {\n    e.stopPropagation();\n    action();\n    setIsDropdownOpen(false);\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 10 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -10 }}\n      transition={{ duration: 0.2 }}\n      className={`relative p-5 rounded-xl border shadow-sm bg-white border-gray-200 transition-all duration-300 cursor-pointer overflow-hidden ${isExpanded ? 'ring-2 ring-black' : ''}`}\n      onClick={toggleExpand}\n    >\n      {/* Animated border highlight on hover */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-blue-900/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300\"></div>\n\n      <div className=\"relative z-10\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-start space-x-4\">\n            <div className=\"mt-1\">\n              <div className=\"w-10 h-10 rounded-full bg-gradient-to-br from-blue-700 to-blue-900 flex items-center justify-center shadow-lg\">\n                {getChainIcon(chain)}\n              </div>\n            </div>\n            <div>\n              <h4 className=\"font-bold text-black flex items-center\">\n                {name}\n                <span className=\"ml-2 px-2 py-0.5 text-xs bg-gray-200 text-black rounded-full\">\n                  {chain}\n                </span>\n              </h4>\n              <div className=\"flex items-center mt-1\">\n                <span className=\"text-sm text-gray-400 font-mono\">\n                  {truncateAddress(address)}\n                </span>\n                <button\n                  onClick={handleCopy}\n                  className=\"ml-2 p-1 rounded-full text-black hover:bg-gray-100 transition-colors\"\n                  aria-label=\"Copy address\"\n                >\n                  <FiCopy size={14} />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"text-right\">\n              <p className=\"text-xs font-medium text-gray-700\">BALANCE</p>\n              <p className=\"text-lg font-bold text-black\">\n                ${balance}\n              </p>\n            </div>\n\n            <div className=\"relative z-20\">\n              <button\n                ref={buttonRef}\n                onClick={toggleDropdown}\n                className=\"p-1.5 rounded-lg hover:bg-gray-100 text-black hover:text-black transition-colors\"\n                aria-label=\"Wallet actions\"\n              >\n                <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <circle cx=\"12\" cy=\"12\" r=\"1\" />\n                  <circle cx=\"12\" cy=\"5\" r=\"1\" />\n                  <circle cx=\"12\" cy=\"19\" r=\"1\" />\n                </svg>\n              </button>\n              {typeof window !== 'undefined' && isDropdownOpen && dropdownPosition &&\n                createPortal(\n                  <motion.div\n                    initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                    animate={{ opacity: 1, y: 0, scale: 1 }}\n                    exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                    transition={{ duration: 0.15, ease: \"easeOut\" }}\n                    className=\"fixed w-56 origin-top-right rounded-xl bg-white border border-gray-200 overflow-hidden z-[9999]\"\n                    style={{\n                      top: dropdownPosition.top,\n                      left: dropdownPosition.left,\n                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n                    }}\n                  >\n                    <div className=\"py-1\">\n                      <button\n                        onClick={(e) => handleAction(e, onDeposit)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors\"\n                      >\n                        <FiDownload className=\"mr-3 text-gray-700\" size={16} />\n                        <span>Deposit</span>\n                      </button>\n                      <button\n                        onClick={(e) => handleAction(e, onWithdraw)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors\"\n                      >\n                        <FiUpload className=\"mr-3 text-gray-700\" size={16} />\n                        <span>Withdraw</span>\n                      </button>\n                      <button\n                        onClick={(e) => handleAction(e, onExport)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors\"\n                      >\n                        <FiDownload className=\"mr-3 text-gray-700\" size={16} />\n                        <span>Export Private Key</span>\n                      </button>\n                      <div className=\"border-t border-gray-200 my-1\"></div>\n                      <button\n                        onClick={(e) => handleAction(e, onEdit)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-black hover:bg-gray-100 hover:text-black transition-colors\"\n                      >\n                        <FiEdit2 className=\"mr-3 text-gray-700\" size={16} />\n                        <span>Edit Wallet</span>\n                      </button>\n                      <button\n                        onClick={(e) => handleAction(e, onArchive)}\n                        className=\"w-full flex items-center px-4 py-2.5 text-sm text-red-600 hover:bg-red-100 transition-colors\"\n                      >\n                        <FiTrash2 className=\"mr-3\" size={16} />\n                        <span>Archive Wallet</span>\n                      </button>\n                    </div>\n                  </motion.div>,\n                  document.body\n                )\n              }\n            </div>\n\n            <button\n              className=\"p-1.5 rounded-lg hover:bg-gray-100 text-black hover:text-black transition-colors\"\n              onClick={toggleExpand}\n              aria-label={isExpanded ? 'Collapse' : 'Expand'}\n            >\n              <svg\n                className={`w-5 h-5 transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <AnimatePresence>\n          {isExpanded && (\n            <motion.div\n              initial={{ height: 0, opacity: 0, marginTop: 0 }}\n              animate={{ height: 'auto', opacity: 1, marginTop: '1rem' }}\n              exit={{ height: 0, opacity: 0, marginTop: 0 }}\n              transition={{ duration: 0.2, ease: \"easeInOut\" }}\n              className=\"overflow-hidden\"\n            >\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h5 className=\"text-sm font-semibold text-gray-700\">ASSETS</h5>\n                  <span className=\"text-xs text-gray-400\">Value</span>\n                </div>\n\n                <div className=\"space-y-3\">\n                  {/* Example asset - in a real app, this would be mapped from wallet assets */}\n                  <div className=\"flex items-center justify-between p-3 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center\">\n                        <FaEthereum className=\"text-black\" size={14} />\n                      </div>\n                      <div>\n                        <div className=\"font-medium text-black\">Ethereum</div>\n                        <div className=\"text-xs text-gray-700\">ETH</div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"font-medium text-black\">$3,450.25</div>\n                      <div className=\"text-xs text-gray-700\">0.5 ETH</div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between p-3 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center\">\n                        <SiBinance className=\"text-black\" size={14} />\n                      </div>\n                      <div>\n                        <div className=\"font-medium text-black\">USD Coin</div>\n                        <div className=\"text-xs text-gray-700\">USDC</div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"font-medium text-black\">$1,250.75</div>\n                      <div className=\"text-xs text-green-600\">1,250.75 USDC</div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"mt-4 pt-3 border-t border-gray-200\">\n                  <button className=\"w-full py-2 px-4 bg-black hover:bg-gray-900 text-white rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-2\">\n                    <span>View All Assets</span>\n                    <svg className=\"w-4 h-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default WalletCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,sBAAsB;AACtB,MAAM,eAAe;IACnB,SAAS;IACT,WAAW;IACX,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,kBAAkB,CAAC,SAAiB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAC1D,IAAI,CAAC,SAAS,OAAO;IACrB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,OAAO,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,MAAM;AAC9D;AAEA,MAAM,eAAe,CAAC;IACpB,OAAQ,MAAM,WAAW;QACvB,KAAK;YACH,qBAAO,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B,KAAK;YACH,qBAAO,8OAAC,8IAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC9B;YACE,qBAAO,8OAAC;gBAAI,WAAU;0BACpB,cAAA,8OAAC;oBAAK,WAAU;8BAAqB,MAAM,MAAM,CAAC;;;;;;;;;;;IAExD;AACF;AAcA,MAAM,aAAa,CAAC,EAClB,IAAI,EACJ,OAAO,EACP,OAAO,EACP,KAAK,EACL,SAAS,EACT,UAAU,EACV,QAAQ,EACR,MAAM,EACN,SAAS,EACO;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwC;IAC/F,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,uCAAuC;IACvC,CAAA,GAAA,8LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;QACxB,IAAI,gBAAgB,kBAAkB;IACxC;IAEA,MAAM,eAAe;QACnB,cAAc,CAAC;QACf,IAAI,gBAAgB,kBAAkB;IACxC;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,eAAe;QACjB,kBAAkB,CAAC;QACnB,IAAI,CAAC,kBAAkB,UAAU,OAAO,EAAE;YACxC,MAAM,OAAO,UAAU,OAAO,CAAC,qBAAqB;YACpD,oBAAoB;gBAClB,KAAK,KAAK,MAAM,GAAG,OAAO,OAAO,GAAG;gBACpC,MAAM,KAAK,KAAK,GAAG,MAAM,OAAO,OAAO;YACzC;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,eAAe;QACjB,UAAU,SAAS,CAAC,SAAS,CAAC;IAC9B,mCAAmC;IACrC;IAEA,MAAM,eAAe,CAAC,GAAqB;QACzC,EAAE,eAAe;QACjB;QACA,kBAAkB;IACpB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,6HAA6H,EAAE,aAAa,sBAAsB,IAAI;QAClL,SAAS;;0BAGT,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,aAAa;;;;;;;;;;;kDAGlB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDACX;kEACD,8OAAC;wDAAK,WAAU;kEACb;;;;;;;;;;;;0DAGL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,gBAAgB;;;;;;kEAEnB,8OAAC;wDACC,SAAS;wDACT,WAAU;wDACV,cAAW;kEAEX,cAAA,8OAAC,8IAAA,CAAA,SAAM;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAA+B;oDACxC;;;;;;;;;;;;;kDAIN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK;gDACL,SAAS;gDACT,WAAU;gDACV,cAAW;0DAEX,cAAA,8OAAC;oDAAI,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;;sEACrI,8OAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;sEAC1B,8OAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAI,GAAE;;;;;;sEACzB,8OAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;;;;;;;;;;;;4CAG7B,gBAAkB,eAAe,kBAAkB,kCAClD,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBACT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDAC1C,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAG,OAAO;gDAAE;gDACtC,MAAM;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDACvC,YAAY;oDAAE,UAAU;oDAAM,MAAM;gDAAU;gDAC9C,WAAU;gDACV,OAAO;oDACL,KAAK,iBAAiB,GAAG;oDACzB,MAAM,iBAAiB,IAAI;oDAC3B,WAAW;gDACb;0DAEA,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,CAAC,IAAM,aAAa,GAAG;4DAChC,WAAU;;8EAEV,8OAAC,8IAAA,CAAA,aAAU;oEAAC,WAAU;oEAAqB,MAAM;;;;;;8EACjD,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DACC,SAAS,CAAC,IAAM,aAAa,GAAG;4DAChC,WAAU;;8EAEV,8OAAC,8IAAA,CAAA,WAAQ;oEAAC,WAAU;oEAAqB,MAAM;;;;;;8EAC/C,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DACC,SAAS,CAAC,IAAM,aAAa,GAAG;4DAChC,WAAU;;8EAEV,8OAAC,8IAAA,CAAA,aAAU;oEAAC,WAAU;oEAAqB,MAAM;;;;;;8EACjD,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DACC,SAAS,CAAC,IAAM,aAAa,GAAG;4DAChC,WAAU;;8EAEV,8OAAC,8IAAA,CAAA,UAAO;oEAAC,WAAU;oEAAqB,MAAM;;;;;;8EAC9C,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DACC,SAAS,CAAC,IAAM,aAAa,GAAG;4DAChC,WAAU;;8EAEV,8OAAC,8IAAA,CAAA,WAAQ;oEAAC,WAAU;oEAAO,MAAM;;;;;;8EACjC,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;sDAIZ,SAAS,IAAI;;;;;;;kDAKnB,8OAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAY,aAAa,aAAa;kDAEtC,cAAA,8OAAC;4CACC,WAAW,CAAC,oDAAoD,EAAE,aAAa,eAAe,IAAI;4CAClG,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7E,8OAAC,yLAAA,CAAA,kBAAe;kCACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ;gCAAG,SAAS;gCAAG,WAAW;4BAAE;4BAC/C,SAAS;gCAAE,QAAQ;gCAAQ,SAAS;gCAAG,WAAW;4BAAO;4BACzD,MAAM;gCAAE,QAAQ;gCAAG,SAAS;gCAAG,WAAW;4BAAE;4BAC5C,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAY;4BAC/C,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAG1C,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8IAAA,CAAA,aAAU;oEAAC,WAAU;oEAAa,MAAM;;;;;;;;;;;0EAE3C,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAyB;;;;;;0EACxC,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAI3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;oEAAC,WAAU;oEAAa,MAAM;;;;;;;;;;;0EAE1C,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAyB;;;;;;0EACxC,8OAAC;gEAAI,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;kDAK9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9D,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3F;uCAEe", "debugId": null}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/utils/web3/config.ts"], "sourcesContent": ["import { createPublicClient, http } from 'viem'\r\nimport { avalanche, base, unichain, berachain, sonic } from 'viem/chains'\r\n\r\nconst systemClient = (chain: any) => {\r\n    return createPublicClient({\r\n        chain,\r\n        transport: http()\r\n    })\r\n}\r\n\r\nexport const avalancheClient = systemClient(avalanche)\r\nexport const baseClient = systemClient(base)\r\nexport const unichainClient = systemClient(unichain)\r\nexport const berachainClient = systemClient(berachain)\r\nexport const sonicClient = systemClient(sonic)\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,eAAe,CAAC;IAClB,OAAO,CAAA,GAAA,6JAAA,CAAA,qBAAkB,AAAD,EAAE;QACtB;QACA,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD;IAClB;AACJ;AAEO,MAAM,kBAAkB,aAAa,kKAAA,CAAA,YAAS;AAC9C,MAAM,aAAa,aAAa,6JAAA,CAAA,OAAI;AACpC,MAAM,iBAAiB,aAAa,iKAAA,CAAA,WAAQ;AAC5C,MAAM,kBAAkB,aAAa,kKAAA,CAAA,YAAS;AAC9C,MAAM,cAAc,aAAa,8JAAA,CAAA,QAAK", "debugId": null}}, {"offset": {"line": 877, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/services/viem/index.ts"], "sourcesContent": ["// import { ethers } from \"ethers\";\r\nimport { avalancheClient, baseClient, unichainClient, berachainClient, sonicClient } from \"@/utils/web3/config\";\r\n\r\nexport const getBalanceForEachChain = async (address: `0x${string}`) => {\r\n    const clients = [\r\n        { name: \"Avalanche\", client: avalancheClient },\r\n        { name: \"Base\", client: baseClient },\r\n        { name: \"Unichain\", client: unichainClient },\r\n        { name: \"<PERSON><PERSON><PERSON><PERSON>\", client: berachainClient },\r\n        { name: \"<PERSON>\", client: sonicClient }\r\n    ];\r\n\r\n    const balances = clients.map(async ({ name, client }) => {\r\n        try {\r\n            const balance = await client.getBalance({ address });\r\n            return { name, balance };\r\n        } catch (error) {\r\n            console.error(`Error fetching balance for ${name}:`, error);\r\n            return { name, balance: BigInt(0) };\r\n        }\r\n    });\r\n\r\n    return Promise.all(balances);\r\n}"], "names": [], "mappings": "AAAA,mCAAmC;;;;AACnC;;AAEO,MAAM,yBAAyB,OAAO;IACzC,MAAM,UAAU;QACZ;YAAE,MAAM;YAAa,QAAQ,8HAAA,CAAA,kBAAe;QAAC;QAC7C;YAAE,MAAM;YAAQ,QAAQ,8HAAA,CAAA,aAAU;QAAC;QACnC;YAAE,MAAM;YAAY,QAAQ,8HAAA,CAAA,iBAAc;QAAC;QAC3C;YAAE,MAAM;YAAa,QAAQ,8HAAA,CAAA,kBAAe;QAAC;QAC7C;YAAE,MAAM;YAAS,QAAQ,8HAAA,CAAA,cAAW;QAAC;KACxC;IAED,MAAM,WAAW,QAAQ,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE;QAChD,IAAI;YACA,MAAM,UAAU,MAAM,OAAO,UAAU,CAAC;gBAAE;YAAQ;YAClD,OAAO;gBAAE;gBAAM;YAAQ;QAC3B,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC,EAAE;YACrD,OAAO;gBAAE;gBAAM,SAAS,OAAO;YAAG;QACtC;IACJ;IAEA,OAAO,QAAQ,GAAG,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/app/terminal/wallets/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { useUserData } from \"@/hooks/useUserData\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect, useState, useCallback, useMemo } from \"react\";\r\nimport { FiPlus, FiFrown as Frown, FiUpload as Upload, FiSearch, FiCopy, FiExternalLink, FiTrash2, FiEdit2, FiCheck, FiX, FiDownload } from \"react-icons/fi\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport WalletCard from \"@/components/Terminal/Wallets/WalletCard\";\r\nimport WalletSelector from \"@/components/Terminal/Wallets/Dropdown\";\r\nimport { Wallet, createWallet, getWallets, importWallet, setPrimaryWallet, getWalletPrivateKey, getWalletBalance } from \"@/services/bot\";\r\nimport { getBalanceForEachChain } from \"@/services/viem\";\r\nimport { chains } from \"@/utils/data\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { toast } from 'react-toastify';\r\n\r\n// Extended Wallet type with UI-specific properties\r\ninterface UIExtendedWallet extends Wallet {\r\n  id: string; // Use address as ID\r\n  name: string;\r\n  chain: string;\r\n  balance: number;\r\n  isPrimary: boolean;\r\n  isTrading: boolean;\r\n  assets?: Array<{\r\n    symbol: string;\r\n    balance: string;\r\n    usdValue: string;\r\n  }>;\r\n  nativeBalances?: Array<{\r\n    name: string;\r\n    balance: bigint;\r\n  }>;\r\n}\r\n\r\n// Helper function to format currency\r\nconst formatCurrency = (value: number): string => {\r\n  return new Intl.NumberFormat('en-US', {\r\n    style: 'currency',\r\n    currency: 'USD',\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2,\r\n  }).format(value);\r\n};\r\n\r\n// Animation variants\r\nconst containerVariants = {\r\n  hidden: { opacity: 0 },\r\n  show: {\r\n    opacity: 1,\r\n    transition: {\r\n      staggerChildren: 0.1,\r\n    },\r\n  },\r\n};\r\n\r\nconst itemVariants = {\r\n  hidden: { opacity: 0, y: 20 },\r\n  show: { opacity: 1, y: 0, transition: { duration: 0.3 } },\r\n};\r\n\r\nexport default function Wallets() {\r\n  const { token } = useAuth();\r\n  const { data: userData } = useUserData(token);\r\n  const { selectedChain } = useWeb3();\r\n  const router = useRouter();\r\n  const [redirecting, setRedirecting] = useState(false);\r\n  // State for wallets and UI\r\n  const [wallets, setWallets] = useState<UIExtendedWallet[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [showCreateModal, setShowCreateModal] = useState(false);\r\n  const [showImportModal, setShowImportModal] = useState(false);\r\n  const [importStep, setImportStep] = useState<1 | 2>(1);\r\n  const [importPrivateKey, setImportPrivateKey] = useState('');\r\n  const [importWalletName, setImportWalletName] = useState('');\r\n  const [isImporting, setIsImporting] = useState(false);\r\n  const [isCreating, setIsCreating] = useState(false);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);\r\n  const [walletName, setWalletName] = useState('');\r\n  const [privateKey, setPrivateKey] = useState('');\r\n  const [expandedWallet, setExpandedWallet] = useState<string | null>(null);\r\n  const [exportedKey, setExportedKey] = useState<string | null>(null);\r\n  const [showExportModal, setShowExportModal] = useState(false);\r\n  const [exportWalletId, setExportWalletId] = useState<string | null>(null);\r\n  const [showDepositModal, setShowDepositModal] = useState(false);\r\n  const [depositWallet, setDepositWallet] = useState<UIExtendedWallet | null>(null);\r\n\r\n  // Fetch wallets and enrich with native balances\r\n  const fetchWallets = useCallback(async () => {\r\n    if (!token) return;\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const response = await getWallets(token);\r\n      if (Array.isArray(response)) {\r\n        // Enrich wallets with native balances from viem\r\n        const walletsWithUIProps: UIExtendedWallet[] = await Promise.all(\r\n          response.map(async (wallet: any) => {\r\n            let nativeBalances: Array<{ name: string; balance: bigint }> = [];\r\n            try {\r\n              nativeBalances = await getBalanceForEachChain(wallet.address);\r\n            } catch (e) {\r\n              nativeBalances = [];\r\n            }\r\n            return {\r\n              ...wallet,\r\n              id: wallet.address,\r\n              name: `Wallet ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`,\r\n              chain: 'Multi',\r\n              balance: 0, // Will be updated by getWalletBalance if needed\r\n              isPrimary: wallet.isPrimary || false,\r\n              isTrading: true,\r\n              assets: [],\r\n              nativeBalances,\r\n            };\r\n          })\r\n        );\r\n        setWallets(walletsWithUIProps);\r\n      } else if (response && typeof response === 'object' && 'error' in response) {\r\n        const errorResponse = response as { error: string };\r\n        throw new Error(errorResponse.error);\r\n      } else {\r\n        throw new Error('Invalid response from server');\r\n      }\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to load wallets';\r\n      setError(`Failed to load wallets: ${errorMessage}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [token]);\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    if (token) {\r\n      fetchWallets();\r\n    }\r\n  }, [token, fetchWallets]);\r\n\r\n  // Handle redirect if no token\r\n  useEffect(() => {\r\n    if (!token && !redirecting) {\r\n      setRedirecting(true);\r\n      router.replace('/terminal/error');\r\n    }\r\n  }, [token, redirecting, router]);\r\n\r\n  // Filter wallets based on search query\r\n  const filteredWallets = useMemo(() => {\r\n    if (!searchQuery.trim()) return wallets;\r\n\r\n    const query = searchQuery.toLowerCase().trim();\r\n    return wallets.filter(wallet =>\r\n      wallet.address.toLowerCase().includes(query) ||\r\n      wallet.name.toLowerCase().includes(query)\r\n    );\r\n  }, [wallets, searchQuery]);\r\n\r\n  // Handle wallet creation\r\n  const handleCreateWallet = async () => {\r\n    if (!token) return;\r\n\r\n    setIsCreating(true);\r\n    try {\r\n      const newWallet = await createWallet(token);\r\n      if (newWallet && 'address' in newWallet) {\r\n        await fetchWallets(); // Refresh the list\r\n        setShowCreateModal(false);\r\n        // TODO: Show success toast\r\n      } else {\r\n        throw new Error('Invalid wallet creation response');\r\n      }\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\r\n      console.error('Failed to create wallet:', errorMessage);\r\n      setError(`Failed to create wallet: ${errorMessage}`);\r\n    } finally {\r\n      setIsCreating(false);\r\n    }\r\n  };\r\n\r\n  // Handle wallet import\r\n  const handleImportWallet = async () => {\r\n    if (!token || !privateKey) return;\r\n\r\n    setIsSubmitting(true);\r\n    try {\r\n      const result = await importWallet(token, { privateKey });\r\n      if (result && 'address' in result) {\r\n        await fetchWallets(); // Refresh the list\r\n        setShowImportModal(false);\r\n        setPrivateKey('');\r\n        setWalletName('');\r\n        // TODO: Show success toast\r\n      } else {\r\n        throw new Error('Invalid import response');\r\n      }\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Invalid private key';\r\n      console.error('Failed to import wallet:', errorMessage);\r\n      setError(`Failed to import wallet: ${errorMessage}`);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Handle set primary wallet\r\n  const handleSetPrimary = async (walletAddress: `0x${string}`) => {\r\n    if (!token) return;\r\n\r\n    try {\r\n      await setPrimaryWallet(token, walletAddress);\r\n      await fetchWallets(); // Refresh the list\r\n      // TODO: Show success toast\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\r\n      console.error('Failed to set primary wallet:', errorMessage);\r\n      setError(`Failed to set primary wallet: ${errorMessage}`);\r\n    }\r\n  };\r\n\r\n  // Handle copy to clipboard\r\n  const handleCopyAddress = (address: string) => {\r\n    navigator.clipboard.writeText(address).catch(err => {\r\n      console.error('Failed to copy address:', err);\r\n      setError('Failed to copy address to clipboard');\r\n    });\r\n    setCopiedAddress(address);\r\n    toast.success('Address copied to clipboard');\r\n    setTimeout(() => setCopiedAddress(null), 2000);\r\n  };\r\n\r\n  // Handle view on explorer\r\n  const handleViewOnExplorer = (address: string) => {\r\n    try {\r\n      const explorerUrl = selectedChain?.scanUrl || 'https://etherscan.io';\r\n      const url = `${explorerUrl}/address/${address}`;\r\n\r\n      window.open(url, '_blank', 'noopener,noreferrer');\r\n    } catch (err) {\r\n      console.error('Failed to open explorer:', err);\r\n      setError('Failed to open blockchain explorer');\r\n    }\r\n  };\r\n\r\n  // Handle wallet archive\r\n  const handleArchive = async (walletId: string) => {\r\n    if (!token) return;\r\n\r\n    try {\r\n      // TODO: Implement archive wallet API call when available\r\n      console.log('Archive wallet:', walletId);\r\n      // Simulate API call\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n      // Optimistic update\r\n      setWallets(prevWallets =>\r\n        prevWallets.filter(wallet => wallet.id !== walletId)\r\n      );\r\n\r\n      // TODO: Show success toast when API is implemented\r\n      console.log('Wallet archived successfully');\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to archive wallet';\r\n      console.error('Failed to archive wallet:', errorMessage);\r\n      setError(`Failed to archive wallet: ${errorMessage}`);\r\n      // Re-fetch to revert optimistic update on error\r\n      fetchWallets();\r\n    }\r\n  };\r\n\r\n  // Handle edit wallet name\r\n  const handleEditName = (walletId: string, newName: string) => {\r\n    // Optimistic update\r\n    setWallets(prevWallets =>\r\n      prevWallets.map(wallet =>\r\n        wallet.id === walletId ? { ...wallet, name: newName } : wallet\r\n      )\r\n    );\r\n\r\n    // TODO: Implement API call to update wallet name when available\r\n    console.log(`Updating wallet ${walletId} name to:`, newName);\r\n  };\r\n\r\n  // Handle wallet export (placeholder for future implementation)\r\n  const handleExportWallet = async (walletId: string) => {\r\n    if (!token) return;\r\n\r\n    try {\r\n      // TODO: Implement secure export when API is available\r\n      console.log('Export wallet requested for:', walletId);\r\n      alert('Wallet export functionality will be available in a future update.');\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to export wallet';\r\n      console.error('Failed to export wallet:', errorMessage);\r\n      setError(`Failed to export wallet: ${errorMessage}`);\r\n    }\r\n  };\r\n\r\n  // Handler for deposit action\r\n  const handleDeposit = (walletId: string) => {\r\n    const wallet = wallets.find(w => w.id === walletId);\r\n    if (wallet) {\r\n      setDepositWallet(wallet);\r\n      setShowDepositModal(true);\r\n    }\r\n  };\r\n\r\n  // Handler for withdraw action\r\n  const handleWithdraw = (walletId: string) => {\r\n    const wallet = wallets.find(w => w.id === walletId);\r\n    if (wallet) {\r\n      console.log(`Withdraw from ${wallet.name}`);\r\n      // TODO: Implement withdraw flow\r\n    }\r\n  };\r\n\r\n  // Handler for edit action\r\n  const handleEdit = (walletId: string) => {\r\n    const wallet = wallets.find(w => w.id === walletId);\r\n    if (wallet) {\r\n      const newName = prompt('Enter new wallet name:', wallet.name);\r\n      if (newName && newName !== wallet.name) {\r\n        handleEditName(walletId, newName);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Calculate total balance (regular function, not a hook)\r\n  const calculateTotalBalance = (wallets: UIExtendedWallet[]): number => {\r\n    let total = 0;\r\n    wallets.forEach(wallet => {\r\n      if (wallet.nativeBalances) {\r\n        wallet.nativeBalances.forEach(chainBal => {\r\n          // For demo, just sum as ETH (should convert to USD with price API)\r\n          total += Number(chainBal.balance) / 1e18;\r\n        });\r\n      }\r\n    });\r\n    return total;\r\n  };\r\n\r\n  // Memoize the total balance calculation\r\n  const totalBalance = calculateTotalBalance(wallets);\r\n\r\n  // Create handlers object\r\n  const walletHandlers = {\r\n    handleDeposit,\r\n    handleWithdraw,\r\n    handleExport: handleExportWallet,\r\n    handleEdit,\r\n    handleArchive,\r\n    handleImport: handleImportWallet,\r\n    handleCopyAddress,\r\n    handleViewOnExplorer,\r\n    handleSetPrimary\r\n  };\r\n\r\n  // Copy address for deposit modal\r\n  const handleCopyDepositAddress = () => {\r\n    if (depositWallet) {\r\n      navigator.clipboard.writeText(depositWallet.address).then(() => {\r\n        toast.success(`Address copied to clipboard (${depositWallet.chain})`);\r\n      }).catch(() => {\r\n        setError('Failed to copy address to clipboard');\r\n      });\r\n    }\r\n  };\r\n\r\n  // Multistep Import Modal Handlers\r\n  const handleOpenImportModal = () => {\r\n    setShowImportModal(true);\r\n    setImportStep(1);\r\n    setImportPrivateKey('');\r\n    setImportWalletName('');\r\n  };\r\n  const handleCloseImportModal = () => {\r\n    setShowImportModal(false);\r\n    setImportStep(1);\r\n    setImportPrivateKey('');\r\n    setImportWalletName('');\r\n  };\r\n  const handleImportNext = () => {\r\n    setImportStep(2);\r\n  };\r\n  const handleImportBack = () => {\r\n    setImportStep(1);\r\n  };\r\n  const handleImportSubmit = async () => {\r\n    if (!token || !importPrivateKey) return;\r\n    setIsImporting(true);\r\n    try {\r\n      await importWallet(token, { privateKey: importPrivateKey, name: importWalletName });\r\n      toast.success('Wallet imported successfully');\r\n      handleCloseImportModal();\r\n      fetchWallets();\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to import wallet';\r\n      setError(errorMessage);\r\n    } finally {\r\n      setIsImporting(false);\r\n    }\r\n  };\r\n\r\n  // --- UI ---\r\n  return (\r\n    <div className=\"min-h-[calc(100vh-80px)] bg-black p-4 md:p-8\">\r\n      {/* Header */}\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\">\r\n          <div>\r\n            <h1 className=\"text-3xl md:text-4xl font-bold text-black\">\r\n              Wallets\r\n            </h1>\r\n            <p className=\"mt-2 text-white\">\r\n              Manage your cryptocurrency wallets and assets\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"mt-4 md:mt-0 flex space-x-3\">\r\n            <button\r\n              onClick={handleOpenImportModal}\r\n              className=\"flex items-center px-4 py-2.5 bg-black text-white rounded-lg transition-colors border border-gray-200\"\r\n            >\r\n              <Upload className=\"mr-2\" size={16} />\r\n              <span>Import</span>\r\n            </button>\r\n            <button\r\n              onClick={() => setShowCreateModal(true)}\r\n              className=\"flex items-center px-4 py-2.5 bg-black text-white rounded-lg transition-colors border border-gray-200\"\r\n            >\r\n              <FiPlus className=\"mr-2\" size={16} />\r\n              <span>New Wallet</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats Cards */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8\">\r\n          <div className=\"bg-white border border-gray-200 rounded-xl p-5 shadow-sm\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-700\">Total Balance ({selectedChain?.symbol})</p>\r\n                <p className=\"text-2xl font-bold mt-1 text-black\">\r\n                  {formatCurrency(totalBalance)}\r\n                </p>\r\n              </div>\r\n              <div className=\"p-3 rounded-full bg-gray-100 text-black\">\r\n                <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white border border-gray-200 rounded-xl p-5 shadow-sm\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-700\">Total Wallets</p>\r\n                <p className=\"text-2xl font-bold mt-1 text-black\">\r\n                  {wallets.length}\r\n                </p>\r\n              </div>\r\n              <div className=\"p-3 rounded-full bg-gray-100 text-black\">\r\n                <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\" />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white border border-gray-200 rounded-xl p-5 shadow-sm\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-700\">Active Chains</p>\r\n                <p className=\"text-2xl font-bold mt-1 text-black\">\r\n                  {new Set(wallets.flatMap(w => (w.nativeBalances || []).map(b => b.name))).size}\r\n                </p>\r\n              </div>\r\n              <div className=\"p-3 rounded-full bg-gray-100 text-black\">\r\n                <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filter */}\r\n        <div className=\"mb-6\">\r\n          <div className=\"relative max-w-md\">\r\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <FiSearch className=\"h-5 w-5 text-gray-700\" />\r\n            </div>\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Search wallets by name or address...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              className=\"block w-full pl-10 pr-3 py-2.5 bg-white border border-gray-200 rounded-lg text-black placeholder-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Wallets List */}\r\n        <div className=\"space-y-4 z-10\">\r\n          {loading ? (\r\n            // Loading skeleton\r\n            <div className=\"space-y-4\">\r\n              {[1, 2, 3].map((i) => (\r\n                <div key={i} className=\"h-24 bg-gray-100 rounded-xl animate-pulse\"></div>\r\n              ))}\r\n            </div>\r\n          ) : filteredWallets.length > 0 ? (\r\n            // Wallets grid\r\n            <motion.div\r\n              variants={containerVariants}\r\n              initial=\"hidden\"\r\n              animate=\"show\"\r\n              className=\"grid grid-cols-1 gap-4 z-10\"\r\n            >\r\n              <AnimatePresence>\r\n                {filteredWallets.map((wallet) => (\r\n                  <motion.div key={wallet.id} variants={itemVariants}>\r\n                    <WalletCard\r\n                      name={wallet.name}\r\n                      address={wallet.address}\r\n                      balance={wallet.nativeBalances?.map(b => `${b.name}: ${(Number(b.balance) / 1e18).toFixed(4)}`).join(' | ') || '0'}\r\n                      chain={wallet.chain}\r\n                      onDeposit={() => handleDeposit(wallet.id)}\r\n                      onWithdraw={() => handleWithdraw(wallet.id)}\r\n                      onExport={() => handleExportWallet(wallet.id)}\r\n                      onEdit={() => handleEdit(wallet.id)}\r\n                      onArchive={() => handleArchive(wallet.id)}\r\n                    />\r\n                    {/* Micro-interaction: Expand for details */}\r\n                    {expandedWallet === wallet.id && (\r\n                      <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 10 }} className=\"p-4 bg-gray-100 rounded-xl mt-2 border border-gray-200\">\r\n                        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\r\n                          <div>\r\n                            <div className=\"font-semibold text-gray-700 mb-2\">Native Balances</div>\r\n                            <ul className=\"text-sm text-black/80\">\r\n                              {wallet.nativeBalances?.map((b, i) => (\r\n                                <li key={i}>{b.name}: {(Number(b.balance) / 1e18).toFixed(4)}</li>\r\n                              ))}\r\n                            </ul>\r\n                          </div>\r\n                          <div>\r\n                            <button onClick={() => handleCopyAddress(wallet.address)} className=\"mr-2 px-3 py-1 bg-gray-100 hover:bg-gray-200 text-black rounded transition-colors\">{copiedAddress === wallet.address ? <FiCheck /> : <FiCopy />} Copy Address</button>\r\n                            <button onClick={() => handleViewOnExplorer(wallet.address)} className=\"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-black rounded transition-colors\"><FiExternalLink /> View on Explorer</button>\r\n                          </div>\r\n                        </div>\r\n                      </motion.div>\r\n                    )}\r\n                  </motion.div>\r\n                ))}\r\n              </AnimatePresence>\r\n            </motion.div>\r\n          ) : (\r\n            // Empty state\r\n            <div className=\"text-center py-16 bg-gray-100 rounded-xl border-2 border-dashed border-gray-200\">\r\n              <Frown className=\"mx-auto h-12 w-12 text-gray-700\" />\r\n              <h3 className=\"mt-2 text-lg font-medium text-gray-700\">No wallets found</h3>\r\n              <p className=\"mt-1 text-sm text-gray-500\">\r\n                {searchQuery ? 'Try a different search term' : 'Get started by creating a new wallet'}\r\n              </p>\r\n              <div className=\"mt-6\">\r\n                <button\r\n                  onClick={handleCreateWallet}\r\n                  disabled={isCreating}\r\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  <FiPlus className=\"-ml-1 mr-2 h-5 w-5\" />\r\n                  New Wallet\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Multistep Import Wallet Modal */}\r\n      {showImportModal && (\r\n        <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\r\n          <div className=\"bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-4\">\r\n                <h3 className=\"text-xl font-bold text-black\">Import Wallet</h3>\r\n                <button\r\n                  onClick={handleCloseImportModal}\r\n                  className=\"text-gray-700 hover:text-black\"\r\n                >\r\n                  <FiX size={24} />\r\n                </button>\r\n              </div>\r\n              {importStep === 1 && (\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <label htmlFor=\"import-wallet-name\" className=\"block text-sm font-medium text-black mb-1\">\r\n                      Wallet Name (Optional)\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"import-wallet-name\"\r\n                      value={importWalletName}\r\n                      onChange={(e) => setImportWalletName(e.target.value)}\r\n                      placeholder=\"e.g. My Imported Wallet\"\r\n                      className=\"w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label htmlFor=\"import-private-key\" className=\"block text-sm font-medium text-black mb-1\">\r\n                      Private Key\r\n                    </label>\r\n                    <textarea\r\n                      id=\"import-private-key\"\r\n                      rows={3}\r\n                      value={importPrivateKey}\r\n                      onChange={(e) => setImportPrivateKey(e.target.value)}\r\n                      placeholder=\"Enter your private key...\"\r\n                      className=\"w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-md text-black font-mono text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\"\r\n                    />\r\n                    <p className=\"mt-1 text-xs text-gray-500\">\r\n                      Your private key is encrypted and never leaves your device.\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"pt-2 flex justify-end gap-2\">\r\n                    <button\r\n                      onClick={handleCloseImportModal}\r\n                      className=\"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-black rounded-md\"\r\n                    >\r\n                      Cancel\r\n                    </button>\r\n                    <button\r\n                      onClick={handleImportNext}\r\n                      disabled={!importPrivateKey || isImporting}\r\n                      className=\"px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                      Next\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n              {importStep === 2 && (\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"bg-gray-50 border border-gray-200 rounded-md p-4\">\r\n                    <div className=\"text-sm text-gray-700 mb-2\">Please confirm the details before importing:</div>\r\n                    <div className=\"mb-1\">\r\n                      <span className=\"font-semibold\">Wallet Name:</span> {importWalletName || <span className=\"italic text-gray-400\">(None)</span>}\r\n                    </div>\r\n                    <div>\r\n                      <span className=\"font-semibold\">Private Key:</span>\r\n                      <span className=\"ml-2 font-mono text-xs text-black bg-gray-100 px-2 py-1 rounded select-all\">{importPrivateKey.slice(0, 6)}...{importPrivateKey.slice(-4)}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"pt-2 flex justify-between gap-2\">\r\n                    <button\r\n                      onClick={handleImportBack}\r\n                      className=\"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-black rounded-md\"\r\n                    >\r\n                      Back\r\n                    </button>\r\n                    <button\r\n                      onClick={handleImportSubmit}\r\n                      disabled={isImporting}\r\n                      className=\"px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                      {isImporting ? 'Importing...' : 'Import Wallet'}\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Create Wallet Modal */}\r\n      {showCreateModal && (\r\n        <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\r\n          <div className=\"bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-4\">\r\n                <h3 className=\"text-xl font-bold text-black\">Create New Wallet</h3>\r\n                <button onClick={() => setShowCreateModal(false)} className=\"text-gray-700 hover:text-white\">\r\n                  <FiX size={24} />\r\n                </button>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <p className=\"text-gray-700\">A new wallet will be generated and securely added to your account.</p>\r\n                <div className=\"pt-2\">\r\n                  <button onClick={handleCreateWallet} disabled={isCreating} className=\"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed\">\r\n                    {isCreating ? 'Creating...' : 'Create Wallet'}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Export Wallet Modal */}\r\n      {showExportModal && (\r\n        <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\r\n          <div className=\"bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-4\">\r\n                <h3 className=\"text-xl font-bold text-black\">Export Private Key</h3>\r\n                <button onClick={() => setShowExportModal(false)} className=\"text-gray-700 hover:text-white\">\r\n                  <FiX size={24} />\r\n                </button>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-black mb-1\">Private Key</label>\r\n                  <textarea readOnly value={exportedKey || ''} className=\"w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-md text-black font-mono text-sm focus:outline-none\" />\r\n                  <p className=\"mt-1 text-xs text-gray-500\">Keep your private key safe. Never share it with anyone.</p>\r\n                </div>\r\n                <div className=\"pt-2 flex justify-end\">\r\n                  <button onClick={() => setShowExportModal(false)} className=\"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-black rounded-md\">Close</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Deposit Modal */}\r\n      {showDepositModal && depositWallet && (\r\n        <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\r\n          <div className=\"bg-white rounded-xl border border-gray-200 w-full max-w-md overflow-hidden shadow-2xl\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-4\">\r\n                <h3 className=\"text-xl font-bold text-black\">Deposit</h3>\r\n                <button\r\n                  onClick={() => setShowDepositModal(false)}\r\n                  className=\"text-gray-700 hover:text-black\"\r\n                >\r\n                  <FiX size={24} />\r\n                </button>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <img src={selectedChain?.logo} alt=\"\" className=\"bg-black rounded-md\" />\r\n                  <div className=\"text-sm font-medium text-black mb-1\">Wallet Address</div>\r\n                  <div className=\"flex items-center bg-gray-100 border border-gray-200 rounded-md px-3 py-2\">\r\n                    <span className=\"font-mono text-black text-xs break-all select-all\">{depositWallet.address}</span>\r\n                    <button\r\n                      onClick={handleCopyDepositAddress}\r\n                      className=\"ml-2 p-1 rounded-full text-black hover:bg-gray-200 transition-colors\"\r\n                      aria-label=\"Copy address\"\r\n                    >\r\n                      <FiCopy size={16} />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div className=\"pt-2 flex justify-end\">\r\n                  <button\r\n                    onClick={() => setShowDepositModal(false)}\r\n                    className=\"px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-md\"\r\n                  >\r\n                    Close\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error Toast */}\r\n      {error && (\r\n        <div className=\"fixed bottom-4 right-4 bg-red-700 text-white px-4 py-2 rounded shadow-lg z-50 animate-pulse\">\r\n          {error}\r\n          <button className=\"ml-2\" onClick={() => setError(null)}><FiX /></button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AAAA;AACA;AAEA;AACA;AAdA;;;;;;;;;;;;;AAmCA,qCAAqC;AACrC,MAAM,iBAAiB,CAAC;IACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEA,qBAAqB;AACrB,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,MAAM;QACJ,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;QAAG,YAAY;YAAE,UAAU;QAAI;IAAE;AAC1D;AAEe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;IACvC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,2BAA2B;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IACpD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAE5E,gDAAgD;IAChD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,OAAO;QACZ,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD,EAAE;YAClC,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,gDAAgD;gBAChD,MAAM,qBAAyC,MAAM,QAAQ,GAAG,CAC9D,SAAS,GAAG,CAAC,OAAO;oBAClB,IAAI,iBAA2D,EAAE;oBACjE,IAAI;wBACF,iBAAiB,MAAM,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,OAAO;oBAC9D,EAAE,OAAO,GAAG;wBACV,iBAAiB,EAAE;oBACrB;oBACA,OAAO;wBACL,GAAG,MAAM;wBACT,IAAI,OAAO,OAAO;wBAClB,MAAM,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI;wBAC1E,OAAO;wBACP,SAAS;wBACT,WAAW,OAAO,SAAS,IAAI;wBAC/B,WAAW;wBACX,QAAQ,EAAE;wBACV;oBACF;gBACF;gBAEF,WAAW;YACb,OAAO,IAAI,YAAY,OAAO,aAAa,YAAY,WAAW,UAAU;gBAC1E,MAAM,gBAAgB;gBACtB,MAAM,IAAI,MAAM,cAAc,KAAK;YACrC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,wBAAwB,EAAE,cAAc;QACpD,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAM;IAEV,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT;QACF;IACF,GAAG;QAAC;QAAO;KAAa;IAExB,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,CAAC,aAAa;YAC1B,eAAe;YACf,OAAO,OAAO,CAAC;QACjB;IACF,GAAG;QAAC;QAAO;QAAa;KAAO;IAE/B,uCAAuC;IACvC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI,OAAO;QAEhC,MAAM,QAAQ,YAAY,WAAW,GAAG,IAAI;QAC5C,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UACtC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAEvC,GAAG;QAAC;QAAS;KAAY;IAEzB,yBAAyB;IACzB,MAAM,qBAAqB;QACzB,IAAI,CAAC,OAAO;QAEZ,cAAc;QACd,IAAI;YACF,MAAM,YAAY,MAAM,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE;YACrC,IAAI,aAAa,aAAa,WAAW;gBACvC,MAAM,gBAAgB,mBAAmB;gBACzC,mBAAmB;YACnB,2BAA2B;YAC7B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,CAAC,yBAAyB,EAAE,cAAc;QACrD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,IAAI,CAAC,SAAS,CAAC,YAAY;QAE3B,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAAE;YAAW;YACtD,IAAI,UAAU,aAAa,QAAQ;gBACjC,MAAM,gBAAgB,mBAAmB;gBACzC,mBAAmB;gBACnB,cAAc;gBACd,cAAc;YACd,2BAA2B;YAC7B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,CAAC,yBAAyB,EAAE,cAAc;QACrD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;YAC9B,MAAM,gBAAgB,mBAAmB;QACzC,2BAA2B;QAC7B,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS,CAAC,8BAA8B,EAAE,cAAc;QAC1D;IACF;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC;QACzB,UAAU,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,CAAC,CAAA;YAC3C,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX;QACA,iBAAiB;QACjB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,IAAI;YACF,MAAM,cAAc,eAAe,WAAW;YAC9C,MAAM,MAAM,GAAG,YAAY,SAAS,EAAE,SAAS;YAE/C,OAAO,IAAI,CAAC,KAAK,UAAU;QAC7B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX;IACF;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,yDAAyD;YACzD,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,oBAAoB;YACpB,WAAW,CAAA,cACT,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;YAG7C,mDAAmD;YACnD,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,CAAC,0BAA0B,EAAE,cAAc;YACpD,gDAAgD;YAChD;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC,UAAkB;QACxC,oBAAoB;QACpB,WAAW,CAAA,cACT,YAAY,GAAG,CAAC,CAAA,SACd,OAAO,EAAE,KAAK,WAAW;oBAAE,GAAG,MAAM;oBAAE,MAAM;gBAAQ,IAAI;QAI5D,gEAAgE;QAChE,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,SAAS,CAAC,EAAE;IACtD;IAEA,+DAA+D;IAC/D,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,sDAAsD;YACtD,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,CAAC,yBAAyB,EAAE,cAAc;QACrD;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,QAAQ;YACV,iBAAiB;YACjB,oBAAoB;QACtB;IACF;IAEA,8BAA8B;IAC9B,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,IAAI,EAAE;QAC1C,gCAAgC;QAClC;IACF;IAEA,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,QAAQ;YACV,MAAM,UAAU,OAAO,0BAA0B,OAAO,IAAI;YAC5D,IAAI,WAAW,YAAY,OAAO,IAAI,EAAE;gBACtC,eAAe,UAAU;YAC3B;QACF;IACF;IAEA,yDAAyD;IACzD,MAAM,wBAAwB,CAAC;QAC7B,IAAI,QAAQ;QACZ,QAAQ,OAAO,CAAC,CAAA;YACd,IAAI,OAAO,cAAc,EAAE;gBACzB,OAAO,cAAc,CAAC,OAAO,CAAC,CAAA;oBAC5B,mEAAmE;oBACnE,SAAS,OAAO,SAAS,OAAO,IAAI;gBACtC;YACF;QACF;QACA,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,eAAe,sBAAsB;IAE3C,yBAAyB;IACzB,MAAM,iBAAiB;QACrB;QACA;QACA,cAAc;QACd;QACA;QACA,cAAc;QACd;QACA;QACA;IACF;IAEA,iCAAiC;IACjC,MAAM,2BAA2B;QAC/B,IAAI,eAAe;YACjB,UAAU,SAAS,CAAC,SAAS,CAAC,cAAc,OAAO,EAAE,IAAI,CAAC;gBACxD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,6BAA6B,EAAE,cAAc,KAAK,CAAC,CAAC,CAAC;YACtE,GAAG,KAAK,CAAC;gBACP,SAAS;YACX;QACF;IACF;IAEA,kCAAkC;IAClC,MAAM,wBAAwB;QAC5B,mBAAmB;QACnB,cAAc;QACd,oBAAoB;QACpB,oBAAoB;IACtB;IACA,MAAM,yBAAyB;QAC7B,mBAAmB;QACnB,cAAc;QACd,oBAAoB;QACpB,oBAAoB;IACtB;IACA,MAAM,mBAAmB;QACvB,cAAc;IAChB;IACA,MAAM,mBAAmB;QACvB,cAAc;IAChB;IACA,MAAM,qBAAqB;QACzB,IAAI,CAAC,SAAS,CAAC,kBAAkB;QACjC,eAAe;QACf,IAAI;YACF,MAAM,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAAE,YAAY;gBAAkB,MAAM;YAAiB;YACjF,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;QACX,SAAU;YACR,eAAe;QACjB;IACF;IAEA,aAAa;IACb,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAG1D,8OAAC;wCAAE,WAAU;kDAAkB;;;;;;;;;;;;0CAKjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,8IAAA,CAAA,WAAM;gDAAC,WAAU;gDAAO,MAAM;;;;;;0DAC/B,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,8OAAC,8IAAA,CAAA,SAAM;gDAAC,WAAU;gDAAO,MAAM;;;;;;0DAC/B,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;;wDAAoC;wDAAgB,eAAe;wDAAO;;;;;;;8DACvF,8OAAC;oDAAE,WAAU;8DACV,eAAe;;;;;;;;;;;;sDAGpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,QAAQ,MAAM;;;;;;;;;;;;sDAGnB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,IAAI,IAAI,QAAQ,OAAO,CAAC,CAAA,IAAK,CAAC,EAAE,cAAc,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,IAAI;;;;;;;;;;;;sDAGlF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBAAI,WAAU;kCACZ,UACC,mBAAmB;sCACnB,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;oCAAY,WAAU;mCAAb;;;;;;;;;mCAGZ,gBAAgB,MAAM,GAAG,IAC3B,eAAe;sCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;sCAEV,cAAA,8OAAC,yLAAA,CAAA,kBAAe;0CACb,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAiB,UAAU;;0DACpC,8OAAC,gKAAA,CAAA,UAAU;gDACT,MAAM,OAAO,IAAI;gDACjB,SAAS,OAAO,OAAO;gDACvB,SAAS,OAAO,cAAc,EAAE,IAAI,CAAA,IAAK,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK,UAAU;gDAC/G,OAAO,OAAO,KAAK;gDACnB,WAAW,IAAM,cAAc,OAAO,EAAE;gDACxC,YAAY,IAAM,eAAe,OAAO,EAAE;gDAC1C,UAAU,IAAM,mBAAmB,OAAO,EAAE;gDAC5C,QAAQ,IAAM,WAAW,OAAO,EAAE;gDAClC,WAAW,IAAM,cAAc,OAAO,EAAE;;;;;;4CAGzC,mBAAmB,OAAO,EAAE,kBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAAC,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAAG,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAAG,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAAG,WAAU;0DAChH,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAmC;;;;;;8EAClD,8OAAC;oEAAG,WAAU;8EACX,OAAO,cAAc,EAAE,IAAI,CAAC,GAAG,kBAC9B,8OAAC;;gFAAY,EAAE,IAAI;gFAAC;gFAAG,CAAC,OAAO,EAAE,OAAO,IAAI,IAAI,EAAE,OAAO,CAAC;;2EAAjD;;;;;;;;;;;;;;;;sEAIf,8OAAC;;8EACC,8OAAC;oEAAO,SAAS,IAAM,kBAAkB,OAAO,OAAO;oEAAG,WAAU;;wEAAqF,kBAAkB,OAAO,OAAO,iBAAG,8OAAC,8IAAA,CAAA,UAAO;;;;iGAAM,8OAAC,8IAAA,CAAA,SAAM;;;;;wEAAI;;;;;;;8EACrN,8OAAC;oEAAO,SAAS,IAAM,qBAAqB,OAAO,OAAO;oEAAG,WAAU;;sFAA+E,8OAAC,8IAAA,CAAA,iBAAc;;;;;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;uCA1BjK,OAAO,EAAE;;;;;;;;;;;;;;mCAoChC,cAAc;sCACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,UAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CACV,cAAc,gCAAgC;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC,8IAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUpD,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;4BAGd,eAAe,mBACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAqB,WAAU;0DAA4C;;;;;;0DAG1F,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAqB,WAAU;0DAA4C;;;;;;0DAG1F,8OAAC;gDACC,IAAG;gDACH,MAAM;gDACN,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAI5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS;gDACT,UAAU,CAAC,oBAAoB;gDAC/B,WAAU;0DACX;;;;;;;;;;;;;;;;;;4BAMN,eAAe,mBACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAmB;oDAAE,kCAAoB,8OAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;;0DAElH,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;;4DAA8E,iBAAiB,KAAK,CAAC,GAAG;4DAAG;4DAAI,iBAAiB,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;;kDAG3J,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS;gDACT,UAAU;gDACV,WAAU;0DAET,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW/C,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAO,SAAS,IAAM,mBAAmB;wCAAQ,WAAU;kDAC1D,cAAA,8OAAC,8IAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,SAAS;4CAAoB,UAAU;4CAAY,WAAU;sDAClE,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU3C,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAO,SAAS,IAAM,mBAAmB;wCAAQ,WAAU;kDAC1D,cAAA,8OAAC,8IAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA4C;;;;;;0DAC7D,8OAAC;gDAAS,QAAQ;gDAAC,OAAO,eAAe;gDAAI,WAAU;;;;;;0DACvD,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAE5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,SAAS,IAAM,mBAAmB;4CAAQ,WAAU;sDAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASvI,oBAAoB,+BACnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,KAAK,eAAe;gDAAM,KAAI;gDAAG,WAAU;;;;;;0DAChD,8OAAC;gDAAI,WAAU;0DAAsC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAqD,cAAc,OAAO;;;;;;kEAC1F,8OAAC;wDACC,SAAS;wDACT,WAAU;wDACV,cAAW;kEAEX,cAAA,8OAAC,8IAAA,CAAA,SAAM;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAIpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWZ,uBACC,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBAAO,WAAU;wBAAO,SAAS,IAAM,SAAS;kCAAO,cAAA,8OAAC,8IAAA,CAAA,MAAG;;;;;;;;;;;;;;;;;;;;;;AAKtE", "debugId": null}}]}