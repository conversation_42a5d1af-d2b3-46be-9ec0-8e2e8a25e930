"use client"
import { useParams } from "next/navigation"
import Trending from "@/components/Terminal/Trade/Trending";
import RecentlyViewed from "@/components/Terminal/Trade/Trending/RecentlyViewed";
import { useWeb3 } from "@/contexts/Web3Context";
import { useEffect } from "react";
import OpenPositions from "@/components/Terminal/Trade/OpenPosition";
import TokenPairData from "@/components/Terminal/Trade/TokenPairData";

export default function TradePage() {
    const { chain, address } = useParams();
    const { addRecentlyViewed, selectedChain } = useWeb3();

    useEffect(() => {
        // Only add if both are present and address is 0x-prefixed
        if (typeof address === 'string' && typeof chain === 'string' && address.startsWith('0x')) {
            addRecentlyViewed(address as `0x${string}`, chain);
        }
    }, [address, chain, addRecentlyViewed]);

    useEffect(() => {
        document.title = "📊 Live Trading Dashboard - Real-Time Crypto Trading | Anubis Terminal";
    }, []);

    return (
        <section className="overflow-hidden grid grid-cols-1 md:grid-cols-12 h-auto md:h-[calc(100vh-80px)]">
            {/* Sidebar: Trending, RecentlyViewed, OpenPositions (hidden on mobile) */}
            <div className="hidden md:grid md:col-span-2 border-b md:border-b-0 md:border-r border-white/10 w-full overflow-x-auto md:overflow-x-hidden h-auto md:h-full grid-rows-3 md:grid-rows-3">
                <Trending />
                <RecentlyViewed />
                <OpenPositions />
            </div>
            {/* Main content area (Order History hidden on mobile if present) */}
            <div className="md:col-span-8 h-auto md:h-full order-2 md:order-none">
                {/* If Order History is a component, wrap it in a div with hidden on mobile */}
                {/* <div className="hidden md:block"><OrderHistory /></div> */}
                <TokenPairData />
            </div>
            {/* Buy/Sell Panel and Stats - sticky/fixed at bottom on mobile */}
            <div className="fixed bottom-0 left-0 right-0 z-50 md:static md:col-span-2 border-t md:border-t-0 md:border-l h-auto md:h-full flex flex-col bg-black/50 border border-white/10 rounded-t-md md:rounded-md p-2 md:p-4 gap-4 order-1 md:order-none md:mt-0 mt-auto">
                {/* Buy/Sell Panel */}
                <div className="mb-4">
                    <div className="flex flex-col md:flex-row gap-2 mb-3">
                        <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-2 rounded-md hover:bg-white/10 transition-colors">Buy</button>
                        <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-2 rounded-md hover:bg-white/10 transition-colors">Sell</button>
                        {/* <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-2 rounded-md hover:bg-white/10 transition-colors">Market</button> */}
                    </div>
                    <div className="mb-3">
                        <input className="w-full bg-black/40 border border-white/10 rounded-md px-3 py-2 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-500 transition" placeholder="Total" />
                        <div className="grid grid-cols-4 gap-2 mt-2">
                            <button className="min-w-[40%] md:min-w-0 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors text-[8px]">50 {selectedChain?.symbol}</button>
                            <button className="min-w-[40%] md:min-w-0 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors text-[8px]">100 {selectedChain?.symbol}</button>
                            <button className="min-w-[40%] md:min-w-0 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors text-[8px]">500 {selectedChain?.symbol}</button>
                            <button className="min-w-[40%] md:min-w-0 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors text-[8px]">1,000 {selectedChain?.symbol}</button>
                        </div>
                    </div>
                    {/* <div className="flex items-center gap-2 mb-3">
                        <input type="checkbox" className="accent-green-500" id="exit-strategy" />
                        <label htmlFor="exit-strategy" className="text-xs text-white/80">Exit Strategy</label>
                    </div> */}
                    <button className="w-full font-orbitron font-semibold bg-white text-black py-2 rounded-md shadow hover:shadow-white/20 transition-all text-base mt-2">Instant trade</button>
                </div>
                {/* Stats Panel */}
                <div className="border border-white/10 rounded-md p-3 text-xs text-white/80 bg-black/40 mb-4">
                    <div className="flex justify-between mb-1">
                        <span>Gas</span>
                        <span>~0.00 ZK</span>
                    </div>
                    <div className="flex justify-between mb-1">
                        <span>Buy</span>
                        <span>--</span>
                    </div>a
                    {/* Data & Security Warnings */}
                    <div className="border border-white/10 rounded-md p-3 text-xs text-white/80 bg-black/40 flex-1 overflow-y-auto">
                        <div className="mb-2 flex items-center gap-2">
                            <span className="text-yellow-400 font-bold">Data & Security</span>
                            <span className="text-red-500">2 warnings</span>
                        </div>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>Snipers: 0/0 (0.00%)</li>
                            <li>First buyers: 0/0 (0.00%)</li>
                            <li>Dev holding: --</li>
                            <li>Top 10 Holders: 93.34%</li>
                            <li>Can't Sell: --</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    )
}