{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/app/terminal/trade/%5Bchain%5D/%5Baddress%5D/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useParams } from \"next/navigation\"\r\nimport { useEffect, useState, useCallback } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n    FiTrendingUp,\r\n    FiTrendingDown,\r\n    FiRefreshCw,\r\n    FiSettings,\r\n    FiShield,\r\n    FiClock,\r\n    FiDollarSign,\r\n    FiPercent,\r\n    FiZap,\r\n    FiTarget,\r\n    FiBarChart,\r\n    FiActivity,\r\n    FiAlertTriangle,\r\n    FiCheck,\r\n    FiX,\r\n    FiArrowUp,\r\n    FiArrowDown,\r\n    FiExternalLink\r\n} from \"react-icons/fi\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport {\r\n    executeTrade,\r\n    executeSell,\r\n    executeSnipe,\r\n    getMarketData,\r\n    getTradingFees,\r\n    getOpenOrders,\r\n    getOrderHistory,\r\n    getPositions,\r\n    type TradeParams,\r\n    type SellParams,\r\n    type Order,\r\n    type Position,\r\n    type MarketData,\r\n    type TradingFees\r\n} from \"@/services/bot\";\r\n\r\n// Trading interface types\r\ntype TradeType = 'buy' | 'sell' | 'snipe';\r\ntype OrderType = 'market' | 'limit';\r\n\r\ninterface TradingState {\r\n    tradeType: TradeType;\r\n    orderType: OrderType;\r\n    amount: string;\r\n    limitPrice: string;\r\n    slippage: number;\r\n    isTrading: boolean;\r\n    autoRefresh: boolean;\r\n}\r\n\r\nexport default function TradePage() {\r\n    const { chain, address } = useParams();\r\n    const { token } = useAuth();\r\n    const { addRecentlyViewed } = useWeb3();\r\n\r\n    // Core state\r\n    const [marketData, setMarketData] = useState<MarketData | null>(null);\r\n    const [tradingFees, setTradingFees] = useState<TradingFees | null>(null);\r\n    const [orders, setOrders] = useState<Order[]>([]);\r\n    const [positions, setPositions] = useState<Position[]>([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [success, setSuccess] = useState<string | null>(null);\r\n\r\n    // Trading state\r\n    const [tradingState, setTradingState] = useState<TradingState>({\r\n        tradeType: 'buy',\r\n        orderType: 'market',\r\n        amount: '',\r\n        limitPrice: '',\r\n        slippage: 1,\r\n        isTrading: false,\r\n        autoRefresh: true\r\n    });\r\n\r\n    // Quick amount buttons\r\n    const quickAmounts = [50, 100, 500, 1000];\r\n    const slippageOptions = [0.5, 1, 2, 5];\r\n\r\n    useEffect(() => {\r\n        // Only add if both are present and address is 0x-prefixed\r\n        if (typeof address === 'string' && typeof chain === 'string' && address.startsWith('0x')) {\r\n            addRecentlyViewed(address as `0x${string}`, chain);\r\n        }\r\n    }, [address, chain, addRecentlyViewed]);\r\n\r\n    useEffect(() => {\r\n        document.title = \"📊 Live Trading Dashboard - Real-Time Crypto Trading | Anubis Terminal\";\r\n    }, []);\r\n\r\n    // Fetch market data for the token\r\n    const fetchMarketData = useCallback(async () => {\r\n        if (!token || !address || !chain) return;\r\n\r\n        try {\r\n            setLoading(true);\r\n            const data = await getMarketData(token, address as string, parseInt(chain as string));\r\n            setMarketData(data);\r\n\r\n            // Also fetch trading fees\r\n            const fees = await getTradingFees(token, {\r\n                token: address as string,\r\n                amount: 1, // Default amount for fee calculation\r\n                orderType: tradingState.orderType,\r\n                slippage: tradingState.slippage\r\n            });\r\n            setTradingFees(fees);\r\n        } catch (err) {\r\n            const errorMessage = err instanceof Error ? err.message : 'Failed to fetch market data';\r\n            console.error('Error fetching market data:', errorMessage);\r\n            setError(errorMessage);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [token, address, chain, tradingState.orderType, tradingState.slippage]);\r\n\r\n    // Fetch orders and positions\r\n    const fetchOrdersAndPositions = useCallback(async () => {\r\n        if (!token) return;\r\n\r\n        try {\r\n            const [ordersData, positionsData] = await Promise.all([\r\n                getOpenOrders(token),\r\n                getPositions(token)\r\n            ]);\r\n\r\n            setOrders(ordersData);\r\n            setPositions(positionsData);\r\n        } catch (err) {\r\n            console.error('Error fetching orders and positions:', err);\r\n            // Don't set error state here to avoid disrupting the UI\r\n        }\r\n    }, [token]);\r\n\r\n    // Initial data fetch\r\n    useEffect(() => {\r\n        if (token && address && chain) {\r\n            fetchMarketData();\r\n            fetchOrdersAndPositions();\r\n        }\r\n    }, [token, address, chain, fetchMarketData, fetchOrdersAndPositions]);\r\n\r\n    // Auto-refresh data\r\n    useEffect(() => {\r\n        if (!tradingState.autoRefresh) return;\r\n\r\n        const intervalId = setInterval(() => {\r\n            fetchMarketData();\r\n            fetchOrdersAndPositions();\r\n        }, 30000); // Refresh every 30 seconds\r\n\r\n        return () => clearInterval(intervalId);\r\n    }, [tradingState.autoRefresh, fetchMarketData, fetchOrdersAndPositions]);\r\n\r\n    // Handle trade execution\r\n    const executeTradingOperation = async () => {\r\n        if (!token || !address || !chain || !tradingState.amount) {\r\n            setError('Please enter a valid amount');\r\n            return;\r\n        }\r\n\r\n        setTradingState(prev => ({ ...prev, isTrading: true }));\r\n\r\n        try {\r\n            const amount = parseFloat(tradingState.amount);\r\n\r\n            if (isNaN(amount) || amount <= 0) {\r\n                throw new Error('Please enter a valid amount');\r\n            }\r\n\r\n            let response;\r\n\r\n            if (tradingState.tradeType === 'buy') {\r\n                const params: TradeParams = {\r\n                    token: address as string,\r\n                    amount,\r\n                    orderType: tradingState.orderType,\r\n                    slippage: tradingState.slippage\r\n                };\r\n\r\n                if (tradingState.orderType === 'limit' && tradingState.limitPrice) {\r\n                    params.limitPrice = parseFloat(tradingState.limitPrice);\r\n                }\r\n\r\n                response = await executeTrade(token, params);\r\n            } else if (tradingState.tradeType === 'sell') {\r\n                const params: SellParams = {\r\n                    token: address as string,\r\n                    percent: amount > 100 ? 100 : amount, // Ensure percent is not over 100%\r\n                    orderType: tradingState.orderType\r\n                };\r\n\r\n                if (tradingState.orderType === 'limit' && tradingState.limitPrice) {\r\n                    params.limitPrice = parseFloat(tradingState.limitPrice);\r\n                }\r\n\r\n                response = await executeSell(token, params);\r\n            } else if (tradingState.tradeType === 'snipe') {\r\n                response = await executeSnipe(token, {\r\n                    token: address as string,\r\n                    amount\r\n                });\r\n            }\r\n\r\n            if (response && response.success) {\r\n                setSuccess(`${tradingState.tradeType.toUpperCase()} order placed successfully!`);\r\n                setTimeout(() => setSuccess(null), 5000);\r\n\r\n                // Reset amount field\r\n                setTradingState(prev => ({ ...prev, amount: '' }));\r\n\r\n                // Refresh data\r\n                fetchOrdersAndPositions();\r\n            } else {\r\n                throw new Error(response?.message || 'Transaction failed');\r\n            }\r\n        } catch (err) {\r\n            const errorMessage = err instanceof Error ? err.message : 'Transaction failed';\r\n            console.error('Trading error:', errorMessage);\r\n            setError(errorMessage);\r\n            setTimeout(() => setError(null), 5000);\r\n        } finally {\r\n            setTradingState(prev => ({ ...prev, isTrading: false }));\r\n        }\r\n    };\r\n\r\n    // Helper functions\r\n    const formatCurrency = (value: number | string | null | undefined): string => {\r\n        if (value === null || value === undefined) return '$0.00';\r\n        const numValue = typeof value === 'string' ? parseFloat(value) : value;\r\n        if (isNaN(numValue)) return '$0.00';\r\n\r\n        return new Intl.NumberFormat('en-US', {\r\n            style: 'currency',\r\n            currency: 'USD',\r\n            minimumFractionDigits: 2,\r\n            maximumFractionDigits: 6\r\n        }).format(numValue);\r\n    };\r\n\r\n    const formatNumber = (value: number | string | null | undefined, decimals: number = 6): string => {\r\n        if (value === null || value === undefined) return '0';\r\n        const numValue = typeof value === 'string' ? parseFloat(value) : value;\r\n        if (isNaN(numValue)) return '0';\r\n\r\n        return new Intl.NumberFormat('en-US', {\r\n            minimumFractionDigits: 0,\r\n            maximumFractionDigits: decimals\r\n        }).format(numValue);\r\n    };\r\n\r\n    const formatPercent = (value: number | null | undefined): string => {\r\n        if (value === null || value === undefined) return '0%';\r\n\r\n        return new Intl.NumberFormat('en-US', {\r\n            style: 'percent',\r\n            minimumFractionDigits: 2,\r\n            maximumFractionDigits: 2,\r\n            signDisplay: 'exceptZero'\r\n        }).format(value / 100);\r\n    };\r\n\r\n    // UI handlers\r\n    const handleTradeTypeChange = (type: TradeType) => {\r\n        setTradingState(prev => ({ ...prev, tradeType: type }));\r\n    };\r\n\r\n    const handleOrderTypeChange = (type: OrderType) => {\r\n        setTradingState(prev => ({ ...prev, orderType: type }));\r\n    };\r\n\r\n    const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const value = e.target.value;\r\n        // Allow only numbers and decimal point\r\n        if (/^[0-9]*\\.?[0-9]*$/.test(value) || value === '') {\r\n            setTradingState(prev => ({ ...prev, amount: value }));\r\n        }\r\n    };\r\n\r\n    const handleLimitPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const value = e.target.value;\r\n        // Allow only numbers and decimal point\r\n        if (/^[0-9]*\\.?[0-9]*$/.test(value) || value === '') {\r\n            setTradingState(prev => ({ ...prev, limitPrice: value }));\r\n        }\r\n    };\r\n\r\n    const handleSlippageChange = (value: number) => {\r\n        setTradingState(prev => ({ ...prev, slippage: value }));\r\n    };\r\n\r\n    const handleQuickAmount = (amount: number) => {\r\n        setTradingState(prev => ({ ...prev, amount: amount.toString() }));\r\n    };\r\n\r\n    const handleRefresh = () => {\r\n        fetchMarketData();\r\n        fetchOrdersAndPositions();\r\n    };\r\n\r\n    const toggleAutoRefresh = () => {\r\n        setTradingState(prev => ({ ...prev, autoRefresh: !prev.autoRefresh }));\r\n    };\r\n\r\n    const viewOnExplorer = () => {\r\n        if (!chain || !address) return;\r\n\r\n        let explorerUrl = '';\r\n\r\n        // Determine explorer URL based on chain\r\n        switch (chain) {\r\n            case '1':\r\n                explorerUrl = `https://etherscan.io/token/${address}`;\r\n                break;\r\n            case '56':\r\n                explorerUrl = `https://bscscan.com/token/${address}`;\r\n                break;\r\n            case '137':\r\n                explorerUrl = `https://polygonscan.com/token/${address}`;\r\n                break;\r\n            case '42161':\r\n                explorerUrl = `https://arbiscan.io/token/${address}`;\r\n                break;\r\n            default:\r\n                explorerUrl = `https://etherscan.io/token/${address}`;\r\n        }\r\n\r\n        window.open(explorerUrl, '_blank', 'noopener,noreferrer');\r\n    };\r\n\r\n    return (\r\n        <div className=\"h-[calc(100vh-80px)] bg-gray-50 overflow-hidden\">\r\n            <div className=\"grid grid-cols-12 h-full gap-4 p-4\">\r\n                {/* Left Sidebar - Market Data & Token Info */}\r\n                <div className=\"col-span-3 space-y-4\">\r\n                    {/* Token Header */}\r\n                    <div className=\"bg-white rounded-xl border border-gray-200 p-6 shadow-sm\">\r\n                        {loading ? (\r\n                            <div className=\"animate-pulse\">\r\n                                <div className=\"h-6 bg-gray-200 rounded w-3/4 mb-2\"></div>\r\n                                <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\r\n                            </div>\r\n                        ) : marketData ? (\r\n                            <div>\r\n                                <div className=\"flex items-center justify-between mb-4\">\r\n                                    <div>\r\n                                        <h1 className=\"text-xl font-bold text-gray-900\">\r\n                                            {marketData.symbol || 'Unknown'}\r\n                                        </h1>\r\n                                        <p className=\"text-sm text-gray-500\">\r\n                                            {marketData.name || 'Unknown Token'}\r\n                                        </p>\r\n                                    </div>\r\n                                    <button\r\n                                        onClick={viewOnExplorer}\r\n                                        className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\r\n                                    >\r\n                                        <FiExternalLink size={16} />\r\n                                    </button>\r\n                                </div>\r\n\r\n                                <div className=\"space-y-3\">\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                        <span className=\"text-sm text-gray-600\">Price</span>\r\n                                        <span className=\"font-semibold text-gray-900\">\r\n                                            {formatCurrency(marketData.priceUsd)}\r\n                                        </span>\r\n                                    </div>\r\n\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                        <span className=\"text-sm text-gray-600\">24h Change</span>\r\n                                        <div className={`flex items-center ${(marketData.priceChange24h || 0) >= 0\r\n                                            ? 'text-green-600'\r\n                                            : 'text-red-600'\r\n                                            }`}>\r\n                                            {(marketData.priceChange24h || 0) >= 0 ? (\r\n                                                <FiArrowUp size={14} className=\"mr-1\" />\r\n                                            ) : (\r\n                                                <FiArrowDown size={14} className=\"mr-1\" />\r\n                                            )}\r\n                                            <span className=\"font-medium\">\r\n                                                {formatPercent(marketData.priceChange24h)}\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                        <span className=\"text-sm text-gray-600\">Market Cap</span>\r\n                                        <span className=\"font-medium text-gray-900\">\r\n                                            {formatCurrency(marketData.fdv)}\r\n                                        </span>\r\n                                    </div>\r\n\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                        <span className=\"text-sm text-gray-600\">24h Volume</span>\r\n                                        <span className=\"font-medium text-gray-900\">\r\n                                            {formatCurrency(marketData.volume24h)}\r\n                                        </span>\r\n                                    </div>\r\n\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                        <span className=\"text-sm text-gray-600\">Liquidity</span>\r\n                                        <span className=\"font-medium text-gray-900\">\r\n                                            {formatCurrency(marketData.liquidity)}\r\n                                        </span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        ) : (\r\n                            <div className=\"text-center py-8\">\r\n                                <FiAlertTriangle className=\"mx-auto h-8 w-8 text-gray-400 mb-2\" />\r\n                                <p className=\"text-sm text-gray-500\">Failed to load token data</p>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Open Positions */}\r\n                    <div className=\"bg-white rounded-xl border border-gray-200 p-6 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between mb-4\">\r\n                            <h3 className=\"font-semibold text-gray-900\">Open Positions</h3>\r\n                            <FiBarChart className=\"text-gray-400\" size={16} />\r\n                        </div>\r\n\r\n                        {positions.length > 0 ? (\r\n                            <div className=\"space-y-3\">\r\n                                {positions.slice(0, 3).map((position) => (\r\n                                    <div key={position.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                                        <div>\r\n                                            <p className=\"font-medium text-gray-900\">{position.tokenSymbol}</p>\r\n                                            <p className=\"text-sm text-gray-500\">{formatNumber(position.amount)} tokens</p>\r\n                                        </div>\r\n                                        <div className=\"text-right\">\r\n                                            <p className={`font-medium ${position.pnl >= 0 ? 'text-green-600' : 'text-red-600'\r\n                                                }`}>\r\n                                                {position.pnl >= 0 ? '+' : ''}{formatCurrency(position.pnl)}\r\n                                            </p>\r\n                                            <p className={`text-sm ${position.pnlPercent >= 0 ? 'text-green-600' : 'text-red-600'\r\n                                                }`}>\r\n                                                {formatPercent(position.pnlPercent)}\r\n                                            </p>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        ) : (\r\n                            <div className=\"text-center py-6\">\r\n                                <FiActivity className=\"mx-auto h-6 w-6 text-gray-400 mb-2\" />\r\n                                <p className=\"text-sm text-gray-500\">No open positions</p>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Center Content - Chart & Orders */}\r\n                <div className=\"col-span-6 space-y-4\">\r\n                    {/* Chart Placeholder */}\r\n                    <div className=\"bg-white rounded-xl border border-gray-200 p-6 shadow-sm h-96\">\r\n                        <div className=\"flex items-center justify-between mb-4\">\r\n                            <h3 className=\"font-semibold text-gray-900\">Price Chart</h3>\r\n                            <div className=\"flex items-center space-x-2\">\r\n                                <button\r\n                                    onClick={handleRefresh}\r\n                                    className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\r\n                                >\r\n                                    <FiRefreshCw size={16} className={loading ? 'animate-spin' : ''} />\r\n                                </button>\r\n                                <button\r\n                                    onClick={toggleAutoRefresh}\r\n                                    className={`p-2 transition-colors ${tradingState.autoRefresh\r\n                                        ? 'text-green-600 hover:text-green-700'\r\n                                        : 'text-gray-400 hover:text-gray-600'\r\n                                        }`}\r\n                                >\r\n                                    <FiActivity size={16} />\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Chart placeholder - in a real app, you'd integrate TradingView or similar */}\r\n                        <div className=\"h-full bg-gray-50 rounded-lg flex items-center justify-center\">\r\n                            <div className=\"text-center\">\r\n                                <FiTrendingUp className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\r\n                                <p className=\"text-gray-500\">Chart integration coming soon</p>\r\n                                <p className=\"text-sm text-gray-400 mt-1\">\r\n                                    Current Price: {formatCurrency(marketData?.priceUsd)}\r\n                                </p>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Recent Orders */}\r\n                    <div className=\"bg-white rounded-xl border border-gray-200 p-6 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between mb-4\">\r\n                            <h3 className=\"font-semibold text-gray-900\">Recent Orders</h3>\r\n                            <FiClock className=\"text-gray-400\" size={16} />\r\n                        </div>\r\n\r\n                        {orders.length > 0 ? (\r\n                            <div className=\"space-y-3\">\r\n                                {orders.slice(0, 5).map((order) => (\r\n                                    <div key={order.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                                        <div className=\"flex items-center space-x-3\">\r\n                                            <div className={`w-2 h-2 rounded-full ${order.status === 'filled' ? 'bg-green-500' :\r\n                                                order.status === 'pending' ? 'bg-yellow-500' :\r\n                                                    order.status === 'cancelled' ? 'bg-gray-500' :\r\n                                                        'bg-red-500'\r\n                                                }`}></div>\r\n                                            <div>\r\n                                                <p className=\"font-medium text-gray-900\">\r\n                                                    {order.type.toUpperCase()} {order.tokenSymbol}\r\n                                                </p>\r\n                                                <p className=\"text-sm text-gray-500\">\r\n                                                    {formatNumber(order.amount)} • {order.orderType}\r\n                                                </p>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div className=\"text-right\">\r\n                                            <p className=\"font-medium text-gray-900\">\r\n                                                {order.price ? formatCurrency(order.price) : '--'}\r\n                                            </p>\r\n                                            <p className={`text-sm capitalize ${order.status === 'filled' ? 'text-green-600' :\r\n                                                order.status === 'pending' ? 'text-yellow-600' :\r\n                                                    order.status === 'cancelled' ? 'text-gray-600' :\r\n                                                        'text-red-600'\r\n                                                }`}>\r\n                                                {order.status}\r\n                                            </p>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        ) : (\r\n                            <div className=\"text-center py-6\">\r\n                                <FiClock className=\"mx-auto h-6 w-6 text-gray-400 mb-2\" />\r\n                                <p className=\"text-sm text-gray-500\">No recent orders</p>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Right Trading Panel */}\r\n                <div className=\"col-span-3 space-y-4\">\r\n                    {/* Trading Panel */}\r\n                    <div className=\"bg-white rounded-xl border border-gray-200 p-6 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between mb-6\">\r\n                            <h3 className=\"font-semibold text-gray-900\">Trade</h3>\r\n                            <FiZap className=\"text-blue-600\" size={16} />\r\n                        </div>\r\n\r\n                        {/* Trade Type Selector */}\r\n                        <div className=\"grid grid-cols-3 gap-2 mb-6\">\r\n                            {(['buy', 'sell', 'snipe'] as TradeType[]).map((type) => (\r\n                                <button\r\n                                    key={type}\r\n                                    onClick={() => handleTradeTypeChange(type)}\r\n                                    className={`py-2 px-3 rounded-lg text-sm font-medium transition-colors ${tradingState.tradeType === type\r\n                                        ? type === 'buy'\r\n                                            ? 'bg-green-100 text-green-700 border border-green-200'\r\n                                            : type === 'sell'\r\n                                                ? 'bg-red-100 text-red-700 border border-red-200'\r\n                                                : 'bg-yellow-100 text-yellow-700 border border-yellow-200'\r\n                                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\r\n                                        }`}\r\n                                >\r\n                                    {type.charAt(0).toUpperCase() + type.slice(1)}\r\n                                </button>\r\n                            ))}\r\n                        </div>\r\n\r\n                        {/* Order Type Selector */}\r\n                        <div className=\"grid grid-cols-2 gap-2 mb-6\">\r\n                            {(['market', 'limit'] as OrderType[]).map((type) => (\r\n                                <button\r\n                                    key={type}\r\n                                    onClick={() => handleOrderTypeChange(type)}\r\n                                    className={`py-2 px-3 rounded-lg text-sm font-medium transition-colors ${tradingState.orderType === type\r\n                                        ? 'bg-blue-100 text-blue-700 border border-blue-200'\r\n                                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\r\n                                        }`}\r\n                                >\r\n                                    {type.charAt(0).toUpperCase() + type.slice(1)}\r\n                                </button>\r\n                            ))}\r\n                        </div>\r\n\r\n                        {/* Amount Input */}\r\n                        <div className=\"mb-4\">\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                {tradingState.tradeType === 'sell' ? 'Percentage to Sell' : 'Amount (USD)'}\r\n                            </label>\r\n                            <div className=\"relative\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    value={tradingState.amount}\r\n                                    onChange={handleAmountChange}\r\n                                    placeholder={tradingState.tradeType === 'sell' ? \"0-100%\" : \"0.00\"}\r\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                                />\r\n                                <div className=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                                    {tradingState.tradeType === 'sell' ? (\r\n                                        <FiPercent className=\"text-gray-400\" size={16} />\r\n                                    ) : (\r\n                                        <FiDollarSign className=\"text-gray-400\" size={16} />\r\n                                    )}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Quick Amount Buttons */}\r\n                        {tradingState.tradeType !== 'sell' && (\r\n                            <div className=\"grid grid-cols-4 gap-2 mb-4\">\r\n                                {quickAmounts.map((amount) => (\r\n                                    <button\r\n                                        key={amount}\r\n                                        onClick={() => handleQuickAmount(amount)}\r\n                                        className=\"py-1.5 px-2 text-xs font-medium bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors\"\r\n                                    >\r\n                                        ${amount}\r\n                                    </button>\r\n                                ))}\r\n                            </div>\r\n                        )}\r\n\r\n                        {/* Sell Percentage Buttons */}\r\n                        {tradingState.tradeType === 'sell' && (\r\n                            <div className=\"grid grid-cols-4 gap-2 mb-4\">\r\n                                {[25, 50, 75, 100].map((percent) => (\r\n                                    <button\r\n                                        key={percent}\r\n                                        onClick={() => handleQuickAmount(percent)}\r\n                                        className=\"py-1.5 px-2 text-xs font-medium bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors\"\r\n                                    >\r\n                                        {percent}%\r\n                                    </button>\r\n                                ))}\r\n                            </div>\r\n                        )}\r\n\r\n                        {/* Limit Price Input */}\r\n                        {tradingState.orderType === 'limit' && (\r\n                            <div className=\"mb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                    Limit Price (USD)\r\n                                </label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    value={tradingState.limitPrice}\r\n                                    onChange={handleLimitPriceChange}\r\n                                    placeholder=\"0.00\"\r\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                                />\r\n                            </div>\r\n                        )}\r\n\r\n                        {/* Slippage Settings */}\r\n                        <div className=\"mb-6\">\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                Slippage Tolerance\r\n                            </label>\r\n                            <div className=\"grid grid-cols-4 gap-2\">\r\n                                {slippageOptions.map((slippage) => (\r\n                                    <button\r\n                                        key={slippage}\r\n                                        onClick={() => handleSlippageChange(slippage)}\r\n                                        className={`py-1.5 px-2 text-xs font-medium rounded transition-colors ${tradingState.slippage === slippage\r\n                                            ? 'bg-blue-100 text-blue-700 border border-blue-200'\r\n                                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\r\n                                            }`}\r\n                                    >\r\n                                        {slippage}%\r\n                                    </button>\r\n                                ))}\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Execute Trade Button */}\r\n                        <button\r\n                            onClick={executeTradingOperation}\r\n                            disabled={!tradingState.amount || tradingState.isTrading || !token}\r\n                            className={`w-full py-3 px-4 rounded-lg font-medium transition-all ${tradingState.tradeType === 'buy'\r\n                                    ? 'bg-green-600 hover:bg-green-700 text-white'\r\n                                    : tradingState.tradeType === 'sell'\r\n                                        ? 'bg-red-600 hover:bg-red-700 text-white'\r\n                                        : 'bg-yellow-600 hover:bg-yellow-700 text-white'\r\n                                } disabled:opacity-50 disabled:cursor-not-allowed`}\r\n                        >\r\n                            {tradingState.isTrading ? (\r\n                                <div className=\"flex items-center justify-center\">\r\n                                    <FiRefreshCw className=\"animate-spin mr-2\" size={16} />\r\n                                    Processing...\r\n                                </div>\r\n                            ) : (\r\n                                `${tradingState.tradeType.charAt(0).toUpperCase() + tradingState.tradeType.slice(1)} ${marketData?.symbol || 'Token'\r\n                                }`\r\n                            )}\r\n                        </button>\r\n                    </div>\r\n\r\n                    {/* Trading Fees & Stats */}\r\n                    <div className=\"bg-white rounded-xl border border-gray-200 p-6 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between mb-4\">\r\n                            <h3 className=\"font-semibold text-gray-900\">Trading Fees</h3>\r\n                            <FiTarget className=\"text-gray-400\" size={16} />\r\n                        </div>\r\n\r\n                        {tradingFees ? (\r\n                            <div className=\"space-y-3\">\r\n                                <div className=\"flex items-center justify-between\">\r\n                                    <span className=\"text-sm text-gray-600\">Gas Fee</span>\r\n                                    <span className=\"font-medium text-gray-900\">{tradingFees.gasFee} ETH</span>\r\n                                </div>\r\n\r\n                                <div className=\"flex items-center justify-between\">\r\n                                    <span className=\"text-sm text-gray-600\">Trading Fee</span>\r\n                                    <span className=\"font-medium text-gray-900\">{tradingFees.tradingFee}%</span>\r\n                                </div>\r\n\r\n                                <div className=\"flex items-center justify-between\">\r\n                                    <span className=\"text-sm text-gray-600\">Slippage</span>\r\n                                    <span className=\"font-medium text-gray-900\">{tradingState.slippage}%</span>\r\n                                </div>\r\n\r\n                                <div className=\"border-t border-gray-200 pt-3\">\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                        <span className=\"text-sm font-medium text-gray-900\">Total Fees</span>\r\n                                        <span className=\"font-semibold text-gray-900\">{tradingFees.totalFee}%</span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        ) : (\r\n                            <div className=\"text-center py-4\">\r\n                                <FiSettings className=\"mx-auto h-6 w-6 text-gray-400 mb-2\" />\r\n                                <p className=\"text-sm text-gray-500\">Fee calculation unavailable</p>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Security Warning */}\r\n                    <div className=\"bg-yellow-50 border border-yellow-200 rounded-xl p-4\">\r\n                        <div className=\"flex items-start\">\r\n                            <FiShield className=\"w-5 h-5 text-yellow-600 mt-0.5 mr-3\" />\r\n                            <div>\r\n                                <h4 className=\"text-sm font-medium text-yellow-900 mb-1\">Security Notice</h4>\r\n                                <p className=\"text-sm text-yellow-700\">\r\n                                    Always verify token contracts and be cautious of high slippage trades.\r\n                                    Never invest more than you can afford to lose.\r\n                                </p>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Success Toast */}\r\n            {success && (\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: 50 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    exit={{ opacity: 0, y: 50 }}\r\n                    className=\"fixed bottom-4 right-4 bg-green-600 text-white px-4 py-3 rounded-lg shadow-lg z-50 flex items-center\"\r\n                >\r\n                    <FiCheck className=\"mr-2\" />\r\n                    {success}\r\n                    <button className=\"ml-3\" onClick={() => setSuccess(null)}>\r\n                        <FiX />\r\n                    </button>\r\n                </motion.div>\r\n            )}\r\n\r\n            {/* Error Toast */}\r\n            {error && (\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: 50 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    exit={{ opacity: 0, y: 50 }}\r\n                    className=\"fixed bottom-4 right-4 bg-red-600 text-white px-4 py-3 rounded-lg shadow-lg z-50 flex items-center\"\r\n                >\r\n                    <FiAlertTriangle className=\"mr-2\" />\r\n                    {error}\r\n                    <button className=\"ml-3\" onClick={() => setError(null)}>\r\n                        <FiX />\r\n                    </button>\r\n                </motion.div>\r\n            )}\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAoBA;AACA;AACA;AAAA;;;AA1BA;;;;;;;;AAyDe,SAAS;;IACpB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEpC,aAAa;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,gBAAgB;IAChB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC3D,WAAW;QACX,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;IACjB;IAEA,uBAAuB;IACvB,MAAM,eAAe;QAAC;QAAI;QAAK;QAAK;KAAK;IACzC,MAAM,kBAAkB;QAAC;QAAK;QAAG;QAAG;KAAE;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,0DAA0D;YAC1D,IAAI,OAAO,YAAY,YAAY,OAAO,UAAU,YAAY,QAAQ,UAAU,CAAC,OAAO;gBACtF,kBAAkB,SAA0B;YAChD;QACJ;8BAAG;QAAC;QAAS;QAAO;KAAkB;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,SAAS,KAAK,GAAG;QACrB;8BAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAChC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO;YAElC,IAAI;gBACA,WAAW;gBACX,MAAM,OAAO,MAAM,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,SAAmB,SAAS;gBACpE,cAAc;gBAEd,0BAA0B;gBAC1B,MAAM,OAAO,MAAM,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;oBACrC,OAAO;oBACP,QAAQ;oBACR,WAAW,aAAa,SAAS;oBACjC,UAAU,aAAa,QAAQ;gBACnC;gBACA,eAAe;YACnB,EAAE,OAAO,KAAK;gBACV,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,SAAS;YACb,SAAU;gBACN,WAAW;YACf;QACJ;iDAAG;QAAC;QAAO;QAAS;QAAO,aAAa,SAAS;QAAE,aAAa,QAAQ;KAAC;IAEzE,6BAA6B;IAC7B,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACxC,IAAI,CAAC,OAAO;YAEZ,IAAI;gBACA,MAAM,CAAC,YAAY,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAClD,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE;oBACd,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE;iBAChB;gBAED,UAAU;gBACV,aAAa;YACjB,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,wCAAwC;YACtD,wDAAwD;YAC5D;QACJ;yDAAG;QAAC;KAAM;IAEV,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,IAAI,SAAS,WAAW,OAAO;gBAC3B;gBACA;YACJ;QACJ;8BAAG;QAAC;QAAO;QAAS;QAAO;QAAiB;KAAwB;IAEpE,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,IAAI,CAAC,aAAa,WAAW,EAAE;YAE/B,MAAM,aAAa;kDAAY;oBAC3B;oBACA;gBACJ;iDAAG,QAAQ,2BAA2B;YAEtC;uCAAO,IAAM,cAAc;;QAC/B;8BAAG;QAAC,aAAa,WAAW;QAAE;QAAiB;KAAwB;IAEvE,yBAAyB;IACzB,MAAM,0BAA0B;QAC5B,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,MAAM,EAAE;YACtD,SAAS;YACT;QACJ;QAEA,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAK,CAAC;QAErD,IAAI;YACA,MAAM,SAAS,WAAW,aAAa,MAAM;YAE7C,IAAI,MAAM,WAAW,UAAU,GAAG;gBAC9B,MAAM,IAAI,MAAM;YACpB;YAEA,IAAI;YAEJ,IAAI,aAAa,SAAS,KAAK,OAAO;gBAClC,MAAM,SAAsB;oBACxB,OAAO;oBACP;oBACA,WAAW,aAAa,SAAS;oBACjC,UAAU,aAAa,QAAQ;gBACnC;gBAEA,IAAI,aAAa,SAAS,KAAK,WAAW,aAAa,UAAU,EAAE;oBAC/D,OAAO,UAAU,GAAG,WAAW,aAAa,UAAU;gBAC1D;gBAEA,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YACzC,OAAO,IAAI,aAAa,SAAS,KAAK,QAAQ;gBAC1C,MAAM,SAAqB;oBACvB,OAAO;oBACP,SAAS,SAAS,MAAM,MAAM;oBAC9B,WAAW,aAAa,SAAS;gBACrC;gBAEA,IAAI,aAAa,SAAS,KAAK,WAAW,aAAa,UAAU,EAAE;oBAC/D,OAAO,UAAU,GAAG,WAAW,aAAa,UAAU;gBAC1D;gBAEA,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD,EAAE,OAAO;YACxC,OAAO,IAAI,aAAa,SAAS,KAAK,SAAS;gBAC3C,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE,OAAO;oBACjC,OAAO;oBACP;gBACJ;YACJ;YAEA,IAAI,YAAY,SAAS,OAAO,EAAE;gBAC9B,WAAW,GAAG,aAAa,SAAS,CAAC,WAAW,GAAG,2BAA2B,CAAC;gBAC/E,WAAW,IAAM,WAAW,OAAO;gBAEnC,qBAAqB;gBACrB,gBAAgB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAG,CAAC;gBAEhD,eAAe;gBACf;YACJ,OAAO;gBACH,MAAM,IAAI,MAAM,UAAU,WAAW;YACzC;QACJ,EAAE,OAAO,KAAK;YACV,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,QAAQ,KAAK,CAAC,kBAAkB;YAChC,SAAS;YACT,WAAW,IAAM,SAAS,OAAO;QACrC,SAAU;YACN,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;QAC1D;IACJ;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACpB,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAClD,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;QACjE,IAAI,MAAM,WAAW,OAAO;QAE5B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAClC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QAC3B,GAAG,MAAM,CAAC;IACd;IAEA,MAAM,eAAe,CAAC,OAA2C,WAAmB,CAAC;QACjF,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAClD,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;QACjE,IAAI,MAAM,WAAW,OAAO;QAE5B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAClC,uBAAuB;YACvB,uBAAuB;QAC3B,GAAG,MAAM,CAAC;IACd;IAEA,MAAM,gBAAgB,CAAC;QACnB,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAElD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAClC,OAAO;YACP,uBAAuB;YACvB,uBAAuB;YACvB,aAAa;QACjB,GAAG,MAAM,CAAC,QAAQ;IACtB;IAEA,cAAc;IACd,MAAM,wBAAwB,CAAC;QAC3B,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAK,CAAC;IACzD;IAEA,MAAM,wBAAwB,CAAC;QAC3B,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAK,CAAC;IACzD;IAEA,MAAM,qBAAqB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,uCAAuC;QACvC,IAAI,oBAAoB,IAAI,CAAC,UAAU,UAAU,IAAI;YACjD,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAM,CAAC;QACvD;IACJ;IAEA,MAAM,yBAAyB,CAAC;QAC5B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,uCAAuC;QACvC,IAAI,oBAAoB,IAAI,CAAC,UAAU,UAAU,IAAI;YACjD,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,YAAY;gBAAM,CAAC;QAC3D;IACJ;IAEA,MAAM,uBAAuB,CAAC;QAC1B,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAM,CAAC;IACzD;IAEA,MAAM,oBAAoB,CAAC;QACvB,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ,OAAO,QAAQ;YAAG,CAAC;IACnE;IAEA,MAAM,gBAAgB;QAClB;QACA;IACJ;IAEA,MAAM,oBAAoB;QACtB,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa,CAAC,KAAK,WAAW;YAAC,CAAC;IACxE;IAEA,MAAM,iBAAiB;QACnB,IAAI,CAAC,SAAS,CAAC,SAAS;QAExB,IAAI,cAAc;QAElB,wCAAwC;QACxC,OAAQ;YACJ,KAAK;gBACD,cAAc,CAAC,2BAA2B,EAAE,SAAS;gBACrD;YACJ,KAAK;gBACD,cAAc,CAAC,0BAA0B,EAAE,SAAS;gBACpD;YACJ,KAAK;gBACD,cAAc,CAAC,8BAA8B,EAAE,SAAS;gBACxD;YACJ,KAAK;gBACD,cAAc,CAAC,0BAA0B,EAAE,SAAS;gBACpD;YACJ;gBACI,cAAc,CAAC,2BAA2B,EAAE,SAAS;QAC7D;QAEA,OAAO,IAAI,CAAC,aAAa,UAAU;IACvC;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC;gCAAI,WAAU;0CACV,wBACG,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAEnB,2BACA,6LAAC;;sDACG,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;;sEACG,6LAAC;4DAAG,WAAU;sEACT,WAAW,MAAM,IAAI;;;;;;sEAE1B,6LAAC;4DAAE,WAAU;sEACR,WAAW,IAAI,IAAI;;;;;;;;;;;;8DAG5B,6LAAC;oDACG,SAAS;oDACT,WAAU;8DAEV,cAAA,6LAAC,iJAAA,CAAA,iBAAc;wDAAC,MAAM;;;;;;;;;;;;;;;;;sDAI9B,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;4DAAK,WAAU;sEACX,eAAe,WAAW,QAAQ;;;;;;;;;;;;8DAI3C,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;4DAAI,WAAW,CAAC,kBAAkB,EAAE,CAAC,WAAW,cAAc,IAAI,CAAC,KAAK,IACnE,mBACA,gBACA;;gEACD,CAAC,WAAW,cAAc,IAAI,CAAC,KAAK,kBACjC,6LAAC,iJAAA,CAAA,YAAS;oEAAC,MAAM;oEAAI,WAAU;;;;;yFAE/B,6LAAC,iJAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAErC,6LAAC;oEAAK,WAAU;8EACX,cAAc,WAAW,cAAc;;;;;;;;;;;;;;;;;;8DAKpD,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;4DAAK,WAAU;sEACX,eAAe,WAAW,GAAG;;;;;;;;;;;;8DAItC,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;4DAAK,WAAU;sEACX,eAAe,WAAW,SAAS;;;;;;;;;;;;8DAI5C,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;4DAAK,WAAU;sEACX,eAAe,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;yDAMpD,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,iJAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAMjD,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;;oCAG/C,UAAU,MAAM,GAAG,kBAChB,6LAAC;wCAAI,WAAU;kDACV,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBACxB,6LAAC;gDAAsB,WAAU;;kEAC7B,6LAAC;;0EACG,6LAAC;gEAAE,WAAU;0EAA6B,SAAS,WAAW;;;;;;0EAC9D,6LAAC;gEAAE,WAAU;;oEAAyB,aAAa,SAAS,MAAM;oEAAE;;;;;;;;;;;;;kEAExE,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAE,WAAW,CAAC,YAAY,EAAE,SAAS,GAAG,IAAI,IAAI,mBAAmB,gBAC9D;;oEACD,SAAS,GAAG,IAAI,IAAI,MAAM;oEAAI,eAAe,SAAS,GAAG;;;;;;;0EAE9D,6LAAC;gEAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,UAAU,IAAI,IAAI,mBAAmB,gBACjE;0EACD,cAAc,SAAS,UAAU;;;;;;;;;;;;;+CAZpC,SAAS,EAAE;;;;;;;;;6DAmB7B,6LAAC;wCAAI,WAAU;;0DACX,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAOrD,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDACG,SAAS;wDACT,WAAU;kEAEV,cAAA,6LAAC,iJAAA,CAAA,cAAW;4DAAC,MAAM;4DAAI,WAAW,UAAU,iBAAiB;;;;;;;;;;;kEAEjE,6LAAC;wDACG,SAAS;wDACT,WAAW,CAAC,sBAAsB,EAAE,aAAa,WAAW,GACtD,wCACA,qCACA;kEAEN,cAAA,6LAAC,iJAAA,CAAA,aAAU;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAM9B,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC,iJAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,6LAAC;oDAAE,WAAU;;wDAA6B;wDACtB,eAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAO3D,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;;oCAG5C,OAAO,MAAM,GAAG,kBACb,6LAAC;wCAAI,WAAU;kDACV,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBACrB,6LAAC;gDAAmB,WAAU;;kEAC1B,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAW,CAAC,qBAAqB,EAAE,MAAM,MAAM,KAAK,WAAW,iBAChE,MAAM,MAAM,KAAK,YAAY,kBACzB,MAAM,MAAM,KAAK,cAAc,gBAC3B,cACN;;;;;;0EACN,6LAAC;;kFACG,6LAAC;wEAAE,WAAU;;4EACR,MAAM,IAAI,CAAC,WAAW;4EAAG;4EAAE,MAAM,WAAW;;;;;;;kFAEjD,6LAAC;wEAAE,WAAU;;4EACR,aAAa,MAAM,MAAM;4EAAE;4EAAI,MAAM,SAAS;;;;;;;;;;;;;;;;;;;kEAI3D,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAE,WAAU;0EACR,MAAM,KAAK,GAAG,eAAe,MAAM,KAAK,IAAI;;;;;;0EAEjD,6LAAC;gEAAE,WAAW,CAAC,mBAAmB,EAAE,MAAM,MAAM,KAAK,WAAW,mBAC5D,MAAM,MAAM,KAAK,YAAY,oBACzB,MAAM,MAAM,KAAK,cAAc,kBAC3B,gBACN;0EACD,MAAM,MAAM;;;;;;;;;;;;;+CAzBf,MAAM,EAAE;;;;;;;;;6DAgC1B,6LAAC;wCAAI,WAAU;;0DACX,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAOrD,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,6LAAC,iJAAA,CAAA,QAAK;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;;kDAI3C,6LAAC;wCAAI,WAAU;kDACV,AAAC;4CAAC;4CAAO;4CAAQ;yCAAQ,CAAiB,GAAG,CAAC,CAAC,qBAC5C,6LAAC;gDAEG,SAAS,IAAM,sBAAsB;gDACrC,WAAW,CAAC,2DAA2D,EAAE,aAAa,SAAS,KAAK,OAC9F,SAAS,QACL,wDACA,SAAS,SACL,kDACA,2DACR,+CACA;0DAEL,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;+CAXtC;;;;;;;;;;kDAiBjB,6LAAC;wCAAI,WAAU;kDACV,AAAC;4CAAC;4CAAU;yCAAQ,CAAiB,GAAG,CAAC,CAAC,qBACvC,6LAAC;gDAEG,SAAS,IAAM,sBAAsB;gDACrC,WAAW,CAAC,2DAA2D,EAAE,aAAa,SAAS,KAAK,OAC9F,qDACA,+CACA;0DAEL,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;+CAPtC;;;;;;;;;;kDAajB,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAM,WAAU;0DACZ,aAAa,SAAS,KAAK,SAAS,uBAAuB;;;;;;0DAEhE,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDACG,MAAK;wDACL,OAAO,aAAa,MAAM;wDAC1B,UAAU;wDACV,aAAa,aAAa,SAAS,KAAK,SAAS,WAAW;wDAC5D,WAAU;;;;;;kEAEd,6LAAC;wDAAI,WAAU;kEACV,aAAa,SAAS,KAAK,uBACxB,6LAAC,iJAAA,CAAA,YAAS;4DAAC,WAAU;4DAAgB,MAAM;;;;;iFAE3C,6LAAC,iJAAA,CAAA,eAAY;4DAAC,WAAU;4DAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;oCAO7D,aAAa,SAAS,KAAK,wBACxB,6LAAC;wCAAI,WAAU;kDACV,aAAa,GAAG,CAAC,CAAC,uBACf,6LAAC;gDAEG,SAAS,IAAM,kBAAkB;gDACjC,WAAU;;oDACb;oDACK;;+CAJG;;;;;;;;;;oCAWpB,aAAa,SAAS,KAAK,wBACxB,6LAAC;wCAAI,WAAU;kDACV;4CAAC;4CAAI;4CAAI;4CAAI;yCAAI,CAAC,GAAG,CAAC,CAAC,wBACpB,6LAAC;gDAEG,SAAS,IAAM,kBAAkB;gDACjC,WAAU;;oDAET;oDAAQ;;+CAJJ;;;;;;;;;;oCAWpB,aAAa,SAAS,KAAK,yBACxB,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACG,MAAK;gDACL,OAAO,aAAa,UAAU;gDAC9B,UAAU;gDACV,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAMtB,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;0DACV,gBAAgB,GAAG,CAAC,CAAC,yBAClB,6LAAC;wDAEG,SAAS,IAAM,qBAAqB;wDACpC,WAAW,CAAC,0DAA0D,EAAE,aAAa,QAAQ,KAAK,WAC5F,qDACA,+CACA;;4DAEL;4DAAS;;uDAPL;;;;;;;;;;;;;;;;kDAcrB,6LAAC;wCACG,SAAS;wCACT,UAAU,CAAC,aAAa,MAAM,IAAI,aAAa,SAAS,IAAI,CAAC;wCAC7D,WAAW,CAAC,uDAAuD,EAAE,aAAa,SAAS,KAAK,QACtF,+CACA,aAAa,SAAS,KAAK,SACvB,2CACA,+CACT,gDAAgD,CAAC;kDAErD,aAAa,SAAS,iBACnB,6LAAC;4CAAI,WAAU;;8DACX,6LAAC,iJAAA,CAAA,cAAW;oDAAC,WAAU;oDAAoB,MAAM;;;;;;gDAAM;;;;;;mDAI3D,GAAG,aAAa,SAAS,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,YAAY,UAAU,SAC3G;;;;;;;;;;;;0CAMd,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;;oCAG7C,4BACG,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;;4DAA6B,YAAY,MAAM;4DAAC;;;;;;;;;;;;;0DAGpE,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;;4DAA6B,YAAY,UAAU;4DAAC;;;;;;;;;;;;;0DAGxE,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;;4DAA6B,aAAa,QAAQ;4DAAC;;;;;;;;;;;;;0DAGvE,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAoC;;;;;;sEACpD,6LAAC;4DAAK,WAAU;;gEAA+B,YAAY,QAAQ;gEAAC;;;;;;;;;;;;;;;;;;;;;;;6DAKhF,6LAAC;wCAAI,WAAU;;0DACX,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,iJAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;;8DACG,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW1D,yBACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACP,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC1B,WAAU;;kCAEV,6LAAC,iJAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAClB;kCACD,6LAAC;wBAAO,WAAU;wBAAO,SAAS,IAAM,WAAW;kCAC/C,cAAA,6LAAC,iJAAA,CAAA,MAAG;;;;;;;;;;;;;;;;YAMf,uBACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACP,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC1B,WAAU;;kCAEV,6LAAC,iJAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;oBAC1B;kCACD,6LAAC;wBAAO,WAAU;wBAAO,SAAS,IAAM,SAAS;kCAC7C,cAAA,6LAAC,iJAAA,CAAA,MAAG;;;;;;;;;;;;;;;;;;;;;;AAM5B;GAhuBwB;;QACO,qIAAA,CAAA,YAAS;QAClB,kIAAA,CAAA,UAAO;QACK,kIAAA,CAAA,UAAO;;;KAHjB", "debugId": null}}]}